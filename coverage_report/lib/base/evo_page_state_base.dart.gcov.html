<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/base/evo_page_state_base.dart</title>
  <link rel="stylesheet" type="text/css" href="../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/base">lib/base</a> - evo_page_state_base.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">95</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'dart:async';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter/material.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/base/page_base.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : import '../data/response/maintenance_info_entity.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../data/response/user_information_entity.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../feature/authorization_session_expired/authorization_session_expired_handler.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../feature/authorization_session_expired/authorization_session_expired_popup.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../feature/biometric/biometric_token_module/biometric_change_mixin.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../feature/biometric/biometric_token_module/biometric_token_usability_mixin.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../feature/logging/metadata_define/evo_event_metadata.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../feature/logging/screen_action_define/special_action_event.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../feature/maintenance/maintenance_handler.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../feature/maintenance/maintenance_screen.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../prepare_for_app_initiation.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../util/evo_authentication_helper.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../util/evo_snackbar.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import '../util/secure_storage_helper/secure_storage_helper.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import '../util/ui_utils/evo_ui_utils.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span>              : abstract class EvoPageStateBase&lt;T extends PageBase&gt; extends PageStateBase&lt;T&gt;</span>
<span id="L29"><span class="lineNum">      29</span>              :     with BiometricChangeMixin, BiometricTokenUsabilityMixin, LogErrorMixin {</span>
<span id="L30"><span class="lineNum">      30</span>              :   @visibleForTesting</span>
<span id="L31"><span class="lineNum">      31</span>              :   StreamSubscription&lt;bool&gt;? authorizationSessionExpiredSubscription;</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span>              :   final AuthorizationSessionExpiredHandler _authorizationSessionExpiredHandler =</span>
<span id="L34"><span class="lineNum">      34</span>              :       getIt.get&lt;AuthorizationSessionExpiredHandler&gt;();</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span>              :   final AuthorizationSessionExpiredPopup _authorizationSessionExpiredPopup =</span>
<span id="L37"><span class="lineNum">      37</span>              :       getIt.get&lt;AuthorizationSessionExpiredPopup&gt;();</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span>              :   @visibleForTesting</span>
<span id="L40"><span class="lineNum">      40</span>              :   StreamSubscription&lt;MaintenanceInfoEntity&gt;? maintenanceSubscription;</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span>              :   final MaintenanceHandler _maintenanceHandler = getIt.get&lt;MaintenanceHandler&gt;();</span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span>              :   final AppState appState = getIt.get&lt;AppState&gt;();</span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span>              :   @visibleForTesting</span>
<span id="L47"><span class="lineNum">      47</span>              :   DateTime? initialTime;</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L50"><span class="lineNum">      50</span>              :   void initState() {</span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :     super.initState();</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :     initialTime = DateTime.now();</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :     setCurrentScreenId();</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :     initAuthorizationSessionExpiredSubscriptionNeed();</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :     initListenMaintenanceIfNeed();</span></span>
<span id="L56"><span class="lineNum">      56</span>              :   }</span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L59"><span class="lineNum">      59</span>              :   void dispose() {</span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :     cancelAuthorizationSessionExpiredSubscriptionIfNeed();</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :     cancelListenMaintenanceIfNeed();</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :     super.dispose();</span></span>
<span id="L63"><span class="lineNum">      63</span>              :   }</span>
<span id="L64"><span class="lineNum">      64</span>              : </span>
<span id="L65"><span class="lineNum">      65</span>              :   /// Called when the top route has been popped off, and the current route</span>
<span id="L66"><span class="lineNum">      66</span>              :   /// shows up.</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L68"><span class="lineNum">      68</span>              :   void didPopNext() {</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :     super.didPopNext();</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :     setCurrentScreenId();</span></span>
<span id="L71"><span class="lineNum">      71</span>              :   }</span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L74"><span class="lineNum">      74</span>              :   void setCurrentScreenId() {</span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :     final EventTrackingScreenId currentScreenId = widget.eventTrackingScreenId;</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :     appState.eventTrackingSharedData.currentScreenId = currentScreenId;</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     commonLog('Current event tracking screen id: ${currentScreenId.name}');</span></span>
<span id="L78"><span class="lineNum">      78</span>              :   }</span>
<span id="L79"><span class="lineNum">      79</span>              : </span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L81"><span class="lineNum">      81</span>              :   Future&lt;void&gt; onResumed() async {</span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :     if (isTopVisible()) {</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :       handleBiometric();</span></span>
<span id="L84"><span class="lineNum">      84</span>              :     }</span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :     super.onResumed();</span></span>
<span id="L86"><span class="lineNum">      86</span>              :   }</span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L89"><span class="lineNum">      89</span>              :   Future&lt;void&gt; handleBiometric() async {</span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :     await handleBiometricChangedIfNeed();</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :     handleBiometricTokenUnUsable();</span></span>
<span id="L92"><span class="lineNum">      92</span>              :   }</span>
<span id="L93"><span class="lineNum">      93</span>              : </span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L95"><span class="lineNum">      95</span>              :   void listenNetworkHandler(bool hasInternet) {}</span>
<span id="L96"><span class="lineNum">      96</span>              : </span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; handleEvoApiError(ErrorUIModel? errorUIModel) async {</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :     switch (errorUIModel?.statusCode) {</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :       case CommonHttpClient.INVALID_TOKEN:</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :         await EvoAuthenticationHelper().clearDataOnTokenInvalid();</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :         if (!hasListenAuthorizationSessionExpired()) {</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :           await showSnackBarError(</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :               errorUIModel?.userMessage ?? getMessageByErrorCode(errorUIModel?.statusCode));</span></span>
<span id="L104"><span class="lineNum">     104</span>              :         }</span>
<span id="L105"><span class="lineNum">     105</span>              :         break;</span>
<span id="L106"><span class="lineNum">     106</span>              :       default:</span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :         await showSnackBarError(</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :             errorUIModel?.userMessage ?? getMessageByErrorCode(errorUIModel?.statusCode));</span></span>
<span id="L109"><span class="lineNum">     109</span>              :         break;</span>
<span id="L110"><span class="lineNum">     110</span>              :     }</span>
<span id="L111"><span class="lineNum">     111</span>              :   }</span>
<span id="L112"><span class="lineNum">     112</span>              : </span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; showSnackBar(</span></span>
<span id="L114"><span class="lineNum">     114</span>              :     String message, {</span>
<span id="L115"><span class="lineNum">     115</span>              :     SnackBarType typeSnackBar = SnackBarType.success,</span>
<span id="L116"><span class="lineNum">     116</span>              :     int? duration,</span>
<span id="L117"><span class="lineNum">     117</span>              :     String? description,</span>
<span id="L118"><span class="lineNum">     118</span>              :     double? marginBottomRatio,</span>
<span id="L119"><span class="lineNum">     119</span>              :   }) async {</span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :     duration ??= SnackBarDuration.short.value;</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :     await getIt.get&lt;EvoSnackBar&gt;().show(</span></span>
<span id="L122"><span class="lineNum">     122</span>              :           message,</span>
<span id="L123"><span class="lineNum">     123</span>              :           typeSnackBar: typeSnackBar,</span>
<span id="L124"><span class="lineNum">     124</span>              :           durationInSec: duration,</span>
<span id="L125"><span class="lineNum">     125</span>              :           description: description,</span>
<span id="L126"><span class="lineNum">     126</span>              :           marginBottomRatio: marginBottomRatio,</span>
<span id="L127"><span class="lineNum">     127</span>              :         );</span>
<span id="L128"><span class="lineNum">     128</span>              :   }</span>
<span id="L129"><span class="lineNum">     129</span>              : </span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; showSnackBarError(</span></span>
<span id="L131"><span class="lineNum">     131</span>              :     String message, {</span>
<span id="L132"><span class="lineNum">     132</span>              :     String? description,</span>
<span id="L133"><span class="lineNum">     133</span>              :     double? marginBottomRatio,</span>
<span id="L134"><span class="lineNum">     134</span>              :   }) async {</span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :     await showSnackBar(</span></span>
<span id="L136"><span class="lineNum">     136</span>              :       message,</span>
<span id="L137"><span class="lineNum">     137</span>              :       typeSnackBar: SnackBarType.error,</span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :       duration: SnackBarDuration.short.value,</span></span>
<span id="L139"><span class="lineNum">     139</span>              :       description: description,</span>
<span id="L140"><span class="lineNum">     140</span>              :       marginBottomRatio: marginBottomRatio,</span>
<span id="L141"><span class="lineNum">     141</span>              :     );</span>
<span id="L142"><span class="lineNum">     142</span>              :   }</span>
<span id="L143"><span class="lineNum">     143</span>              : </span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; showSnackBarWarning(</span></span>
<span id="L145"><span class="lineNum">     145</span>              :     String message, {</span>
<span id="L146"><span class="lineNum">     146</span>              :     String? description,</span>
<span id="L147"><span class="lineNum">     147</span>              :     double? marginBottomRatio,</span>
<span id="L148"><span class="lineNum">     148</span>              :   }) async {</span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :     await showSnackBar(</span></span>
<span id="L150"><span class="lineNum">     150</span>              :       message,</span>
<span id="L151"><span class="lineNum">     151</span>              :       typeSnackBar: SnackBarType.warning,</span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :       duration: SnackBarDuration.short.value,</span></span>
<span id="L153"><span class="lineNum">     153</span>              :       description: description,</span>
<span id="L154"><span class="lineNum">     154</span>              :       marginBottomRatio: marginBottomRatio,</span>
<span id="L155"><span class="lineNum">     155</span>              :     );</span>
<span id="L156"><span class="lineNum">     156</span>              :   }</span>
<span id="L157"><span class="lineNum">     157</span>              : </span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; showSnackBarNeutral(</span></span>
<span id="L159"><span class="lineNum">     159</span>              :     String message, {</span>
<span id="L160"><span class="lineNum">     160</span>              :     String? description,</span>
<span id="L161"><span class="lineNum">     161</span>              :     double? marginBottomRatio,</span>
<span id="L162"><span class="lineNum">     162</span>              :   }) async {</span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :     await showSnackBar(</span></span>
<span id="L164"><span class="lineNum">     164</span>              :       message,</span>
<span id="L165"><span class="lineNum">     165</span>              :       typeSnackBar: SnackBarType.neutral,</span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :       duration: SnackBarDuration.short.value,</span></span>
<span id="L167"><span class="lineNum">     167</span>              :       description: description,</span>
<span id="L168"><span class="lineNum">     168</span>              :       marginBottomRatio: marginBottomRatio,</span>
<span id="L169"><span class="lineNum">     169</span>              :     );</span>
<span id="L170"><span class="lineNum">     170</span>              :   }</span>
<span id="L171"><span class="lineNum">     171</span>              : </span>
<span id="L172"><span class="lineNum">     172</span> <span class="tlaUNC">           0 :   void updateUserLoginStatus(bool isLogin) {</span></span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaUNC">           0 :     final AppState appState = getIt.get&lt;AppState&gt;();</span></span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaUNC">           0 :     appState.isUserLogIn = isLogin;</span></span>
<span id="L175"><span class="lineNum">     175</span>              :   }</span>
<span id="L176"><span class="lineNum">     176</span>              : </span>
<span id="L177"><span class="lineNum">     177</span> <span class="tlaUNC">           0 :   Future&lt;bool&gt; isNewDevice() async {</span></span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaUNC">           0 :     final EvoLocalStorageHelper evoLocalStorageHelper = getIt.get&lt;EvoLocalStorageHelper&gt;();</span></span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :     return await evoLocalStorageHelper.isNewDevice();</span></span>
<span id="L180"><span class="lineNum">     180</span>              :   }</span>
<span id="L181"><span class="lineNum">     181</span>              : </span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :   void updateUserInfo(UserInformationEntity? value) {</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :     final AppState appState = getIt.get&lt;AppState&gt;();</span></span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaUNC">           0 :     appState.userInfo.update(value);</span></span>
<span id="L185"><span class="lineNum">     185</span>              :   }</span>
<span id="L186"><span class="lineNum">     186</span>              : </span>
<span id="L187"><span class="lineNum">     187</span>              :   /// If you do not want to listen Authorization SessionExpired</span>
<span id="L188"><span class="lineNum">     188</span>              :   /// Should Override method and return false</span>
<span id="L189"><span class="lineNum">     189</span>              :   /// ```dart</span>
<span id="L190"><span class="lineNum">     190</span>              :   ///   @override</span>
<span id="L191"><span class="lineNum">     191</span>              :   ///   bool hasListenAuthorizationSessionExpired() =&gt; false;</span>
<span id="L192"><span class="lineNum">     192</span>              :   /// ```</span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaUNC">           0 :   bool hasListenAuthorizationSessionExpired() =&gt; true;</span></span>
<span id="L194"><span class="lineNum">     194</span>              : </span>
<span id="L195"><span class="lineNum">     195</span>              :   /// In case you don't want to listen maintenance mode</span>
<span id="L196"><span class="lineNum">     196</span>              :   ///</span>
<span id="L197"><span class="lineNum">     197</span>              :   /// @override</span>
<span id="L198"><span class="lineNum">     198</span>              :   /// bool hasListenMaintenanceMode() = false</span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaUNC">           0 :   bool hasListenMaintenanceMode() =&gt; true;</span></span>
<span id="L200"><span class="lineNum">     200</span>              : </span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L202"><span class="lineNum">     202</span>              :   void initAuthorizationSessionExpiredSubscriptionNeed() {</span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :     if (hasListenAuthorizationSessionExpired()) {</span></span>
<span id="L204"><span class="lineNum">     204</span> <span class="tlaUNC">           0 :       authorizationSessionExpiredSubscription = _authorizationSessionExpiredHandler</span></span>
<span id="L205"><span class="lineNum">     205</span> <span class="tlaUNC">           0 :           .getStreamSubscription()</span></span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaUNC">           0 :           .listen((bool isExpired) async {</span></span>
<span id="L207"><span class="lineNum">     207</span> <span class="tlaUNC">           0 :         if (mounted &amp;&amp; isExpired) {</span></span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaUNC">           0 :           await EvoAuthenticationHelper().clearDataOnTokenInvalid();</span></span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaUNC">           0 :           showAuthorizationSessionTimeoutPopupIfNeed();</span></span>
<span id="L210"><span class="lineNum">     210</span>              :         }</span>
<span id="L211"><span class="lineNum">     211</span>              :       });</span>
<span id="L212"><span class="lineNum">     212</span>              :     }</span>
<span id="L213"><span class="lineNum">     213</span>              :   }</span>
<span id="L214"><span class="lineNum">     214</span>              : </span>
<span id="L215"><span class="lineNum">     215</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L216"><span class="lineNum">     216</span>              :   void cancelAuthorizationSessionExpiredSubscriptionIfNeed() {</span>
<span id="L217"><span class="lineNum">     217</span> <span class="tlaUNC">           0 :     if (hasListenAuthorizationSessionExpired()) {</span></span>
<span id="L218"><span class="lineNum">     218</span> <span class="tlaUNC">           0 :       authorizationSessionExpiredSubscription?.cancel();</span></span>
<span id="L219"><span class="lineNum">     219</span>              :     }</span>
<span id="L220"><span class="lineNum">     220</span>              :   }</span>
<span id="L221"><span class="lineNum">     221</span>              : </span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L223"><span class="lineNum">     223</span>              :   void showAuthorizationSessionTimeoutPopupIfNeed() {</span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaUNC">           0 :     if (_authorizationSessionExpiredPopup.checkCanShowPopup()) {</span></span>
<span id="L225"><span class="lineNum">     225</span> <span class="tlaUNC">           0 :       _authorizationSessionExpiredPopup.show();</span></span>
<span id="L226"><span class="lineNum">     226</span>              :     }</span>
<span id="L227"><span class="lineNum">     227</span>              :   }</span>
<span id="L228"><span class="lineNum">     228</span>              : </span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L230"><span class="lineNum">     230</span>              :   void initListenMaintenanceIfNeed() {</span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaUNC">           0 :     if (hasListenMaintenanceMode()) {</span></span>
<span id="L232"><span class="lineNum">     232</span> <span class="tlaUNC">           0 :       maintenanceSubscription = _maintenanceHandler</span></span>
<span id="L233"><span class="lineNum">     233</span> <span class="tlaUNC">           0 :           .getStreamSubscription()</span></span>
<span id="L234"><span class="lineNum">     234</span> <span class="tlaUNC">           0 :           .listen((MaintenanceInfoEntity maintain) async {</span></span>
<span id="L235"><span class="lineNum">     235</span> <span class="tlaUNC">           0 :         if (mounted &amp;&amp; !_maintenanceHandler.isHandledMaintenance()) {</span></span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaUNC">           0 :           EvoUiUtils().hideHudLoading();</span></span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaUNC">           0 :           _maintenanceHandler.setHandledMaintenance(true);</span></span>
<span id="L238"><span class="lineNum">     238</span>              : </span>
<span id="L239"><span class="lineNum">     239</span> <span class="tlaUNC">           0 :           MaintenanceScreen.pushReplacementNamed(arg: MaintenanceArg(maintain));</span></span>
<span id="L240"><span class="lineNum">     240</span>              :         }</span>
<span id="L241"><span class="lineNum">     241</span>              :       });</span>
<span id="L242"><span class="lineNum">     242</span>              :     }</span>
<span id="L243"><span class="lineNum">     243</span>              :   }</span>
<span id="L244"><span class="lineNum">     244</span>              : </span>
<span id="L245"><span class="lineNum">     245</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L246"><span class="lineNum">     246</span>              :   void cancelListenMaintenanceIfNeed() {</span>
<span id="L247"><span class="lineNum">     247</span> <span class="tlaUNC">           0 :     if (hasListenMaintenanceMode()) {</span></span>
<span id="L248"><span class="lineNum">     248</span> <span class="tlaUNC">           0 :       maintenanceSubscription?.cancel();</span></span>
<span id="L249"><span class="lineNum">     249</span>              :     }</span>
<span id="L250"><span class="lineNum">     250</span>              :   }</span>
<span id="L251"><span class="lineNum">     251</span>              : </span>
<span id="L252"><span class="lineNum">     252</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L253"><span class="lineNum">     253</span>              :   void didChangeAppLifecycleState(AppLifecycleState state) {</span>
<span id="L254"><span class="lineNum">     254</span> <span class="tlaUNC">           0 :     appState.appLifecycleState = state;</span></span>
<span id="L255"><span class="lineNum">     255</span> <span class="tlaUNC">           0 :     super.didChangeAppLifecycleState(state);</span></span>
<span id="L256"><span class="lineNum">     256</span>              :   }</span>
<span id="L257"><span class="lineNum">     257</span>              : </span>
<span id="L258"><span class="lineNum">     258</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L259"><span class="lineNum">     259</span>              :   int getDurationSinceInitialTimeInMilliseconds() {</span>
<span id="L260"><span class="lineNum">     260</span> <span class="tlaUNC">           0 :     final DateTime? time = initialTime;</span></span>
<span id="L261"><span class="lineNum">     261</span>              :     if (time == null) {</span>
<span id="L262"><span class="lineNum">     262</span>              :       return 0;</span>
<span id="L263"><span class="lineNum">     263</span>              :     }</span>
<span id="L264"><span class="lineNum">     264</span> <span class="tlaUNC">           0 :     return DateTime.now().difference(time).inMilliseconds;</span></span>
<span id="L265"><span class="lineNum">     265</span>              :   }</span>
<span id="L266"><span class="lineNum">     266</span>              : </span>
<span id="L267"><span class="lineNum">     267</span> <span class="tlaUNC">           0 :   void logScreenLoadedEvent({</span></span>
<span id="L268"><span class="lineNum">     268</span>              :     Map&lt;String, dynamic&gt;? extraMeta,</span>
<span id="L269"><span class="lineNum">     269</span>              :   }) {</span>
<span id="L270"><span class="lineNum">     270</span> <span class="tlaUNC">           0 :     getIt&lt;EvoEventTrackingUtils&gt;().sendEvoSpecialEvent(</span></span>
<span id="L271"><span class="lineNum">     271</span>              :       eventActionId: SpecialActionEvent.screenLoaded,</span>
<span id="L272"><span class="lineNum">     272</span> <span class="tlaUNC">           0 :       screenId: widget.eventTrackingScreenId,</span></span>
<span id="L273"><span class="lineNum">     273</span> <span class="tlaUNC">           0 :       metaData: &lt;String, dynamic&gt;{</span></span>
<span id="L274"><span class="lineNum">     274</span> <span class="tlaUNC">           0 :         EvoEventMetadataKey.loadingTime: getDurationSinceInitialTimeInMilliseconds(),</span></span>
<span id="L275"><span class="lineNum">     275</span> <span class="tlaUNC">           0 :         ...?extraMeta,</span></span>
<span id="L276"><span class="lineNum">     276</span>              :       },</span>
<span id="L277"><span class="lineNum">     277</span>              :     );</span>
<span id="L278"><span class="lineNum">     278</span>              :   }</span>
<span id="L279"><span class="lineNum">     279</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
