<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/activated_pos_limit/activate_pos/cubit/activate_pos_limit_cubit.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/activated_pos_limit/activate_pos/cubit">lib/feature/activated_pos_limit/activate_pos/cubit</a> - activate_pos_limit_cubit.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">46</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/base/common_cubit.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../../../../data/response/card_activate_entity.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../../../../data/response/setup_pos_limit_entity.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../../../../util/functions.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../../../data/repository/user_repo.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../prepare_for_app_initiation.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../../util/secure_storage_helper/secure_storage_helper.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../feature_toggle.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../utils/activate_pos_limit_utils.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../mock/mock_activate_pos_limit_use_case.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span>              : part 'activate_pos_limit_state.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              : class ActivatePosLimitCubit extends CommonCubit&lt;ActivatePOSState&gt; with LogErrorMixin {</span>
<span id="L20"><span class="lineNum">      20</span>              :   final UserRepo userRepo;</span>
<span id="L21"><span class="lineNum">      21</span>              :   final AppState appState;</span>
<span id="L22"><span class="lineNum">      22</span>              :   final EvoLocalStorageHelper evoLocalStorageHelper;</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :   ActivatePosLimitCubit({</span></span>
<span id="L25"><span class="lineNum">      25</span>              :     required this.userRepo,</span>
<span id="L26"><span class="lineNum">      26</span>              :     required this.appState,</span>
<span id="L27"><span class="lineNum">      27</span>              :     required this.evoLocalStorageHelper,</span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :   }) : super(ActivatePOSInitial());</span></span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; activatePosLimit(int amount) async {</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :     emit(ActivatePOSLoading());</span></span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :     final SetPOSLimitEntity entity = await userRepo.setPOSLimit(</span></span>
<span id="L34"><span class="lineNum">      34</span>              :         posLimit: amount,</span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :         mockConfig: MockConfig(</span></span>
<span id="L36"><span class="lineNum">      36</span>              :           enable: false,</span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :           fileName: getMockActivatePosLimitUseCase(MockActivatePosLimitUseCase.activatePosSuccess),</span></span>
<span id="L38"><span class="lineNum">      38</span>              :         ));</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :     if (entity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :       emit(ActivatePOSSucceed(entity: entity));</span></span>
<span id="L42"><span class="lineNum">      42</span>              :       return;</span>
<span id="L43"><span class="lineNum">      43</span>              :     }</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :     if (entity.statusCode == CommonHttpClient.BAD_REQUEST &amp;&amp;</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :         entity.verdict == SetPOSLimitEntity.verdictRedirectTPBApp) {</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :       emit(ActivatePOSRedirectToTPBAppState(error: ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L48"><span class="lineNum">      48</span>              :       return;</span>
<span id="L49"><span class="lineNum">      49</span>              :     }</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :     emit(ActivatePOSError(error: ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L52"><span class="lineNum">      52</span>              :   }</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; activateCard(int amount) async {</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :     emit(ActivatePOSLoading());</span></span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :     final CardActivateEntity entity = await userRepo.activateCard(</span></span>
<span id="L58"><span class="lineNum">      58</span>              :         posLimit: amount,</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :         mockConfig: MockConfig(</span></span>
<span id="L60"><span class="lineNum">      60</span>              :           enable: false,</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :           fileName: getMockActivatePosLimitUseCase(MockActivatePosLimitUseCase.activateCardSuccess),</span></span>
<span id="L62"><span class="lineNum">      62</span>              :         ));</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :     if (entity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L65"><span class="lineNum">      65</span>              :       /// Save time to calculate time to wait for next request 3DS OTP</span>
<span id="L66"><span class="lineNum">      66</span>              :       /// The time will be saved when the redirect URL is successfully generated,</span>
<span id="L67"><span class="lineNum">      67</span>              :       /// Which means the API response is successful (status code 200) and contains the redirect URL</span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :       evoLocalStorageHelper</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :           .setLastTimeRequest3DSCardActivation(DateTime.now().millisecondsSinceEpoch);</span></span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :       emit(ActivateCardSucceed(entity: entity));</span></span>
<span id="L72"><span class="lineNum">      72</span>              :       return;</span>
<span id="L73"><span class="lineNum">      73</span>              :     }</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :     if (entity.statusCode == CommonHttpClient.DUPLICATE &amp;&amp;</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :         entity.verdict == CardActivateEntity.verdictInvalidState) {</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :       emit(ActivateCardInvalidState());</span></span>
<span id="L78"><span class="lineNum">      78</span>              :       return;</span>
<span id="L79"><span class="lineNum">      79</span>              :     }</span>
<span id="L80"><span class="lineNum">      80</span>              : </span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :     if (entity.statusCode == CommonHttpClient.BAD_REQUEST &amp;&amp;</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :         entity.verdict == CardActivateEntity.verdictRedirectTPBApp) {</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :       emit(ActivatePOSRedirectToTPBAppState(error: ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L84"><span class="lineNum">      84</span>              :       return;</span>
<span id="L85"><span class="lineNum">      85</span>              :     }</span>
<span id="L86"><span class="lineNum">      86</span>              : </span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :     emit(ActivateCardError(error: ErrorUIModel.fromEntity(entity)));</span></span>
<span id="L88"><span class="lineNum">      88</span>              :   }</span>
<span id="L89"><span class="lineNum">      89</span>              : </span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :   void onChangePosLimitAmount({required String? amount, required int posLimitAllow}) {</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :     final int? orderAmount = appState.activatedPOSLimitState.orderAmount;</span></span>
<span id="L92"><span class="lineNum">      92</span>              : </span>
<span id="L93"><span class="lineNum">      93</span>              :     if (amount == null) {</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :       emit(ActivatePOSValidateFailed());</span></span>
<span id="L95"><span class="lineNum">      95</span>              :       return;</span>
<span id="L96"><span class="lineNum">      96</span>              :     }</span>
<span id="L97"><span class="lineNum">      97</span>              : </span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :     final int amountInt = evoUtilFunction.getAmountFromStr(amount) ?? 0;</span></span>
<span id="L99"><span class="lineNum">      99</span>              : </span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :     if (amountInt &lt; (orderAmount ?? 0) || amountInt &gt; posLimitAllow) {</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :       emit(ActivatePOSValidateFailed());</span></span>
<span id="L102"><span class="lineNum">     102</span>              :     } else {</span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :       emit(ActivatePOSValidateSuccess());</span></span>
<span id="L104"><span class="lineNum">     104</span>              :     }</span>
<span id="L105"><span class="lineNum">     105</span>              :   }</span>
<span id="L106"><span class="lineNum">     106</span>              : </span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; checkShowWaitingPopUp() async {</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :     if (getIt.get&lt;FeatureToggle&gt;().enableActivatePOSLimitFeature == false) {</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :       emit(NoNeedShowWaitingPopup());</span></span>
<span id="L110"><span class="lineNum">     110</span>              :       return;</span>
<span id="L111"><span class="lineNum">     111</span>              :     }</span>
<span id="L112"><span class="lineNum">     112</span>              : </span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :     final int? savedTime = await evoLocalStorageHelper.getLastTimeRequest3DSCardActivation();</span></span>
<span id="L114"><span class="lineNum">     114</span>              : </span>
<span id="L115"><span class="lineNum">     115</span>              :     if (savedTime == null) {</span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :       emit(NoNeedShowWaitingPopup());</span></span>
<span id="L117"><span class="lineNum">     117</span>              :       return;</span>
<span id="L118"><span class="lineNum">     118</span>              :     }</span>
<span id="L119"><span class="lineNum">     119</span>              : </span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :     final int remainingTime = ActivatePosLimitUtils().calculateRemainingCountdownTime(savedTime);</span></span>
<span id="L121"><span class="lineNum">     121</span>              : </span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :     if (remainingTime &gt; 0) {</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :       emit(NeedShowWaitingPopup(remainingTime));</span></span>
<span id="L124"><span class="lineNum">     124</span>              :       return;</span>
<span id="L125"><span class="lineNum">     125</span>              :     }</span>
<span id="L126"><span class="lineNum">     126</span>              : </span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :     evoLocalStorageHelper.setLastTimeRequest3DSCardActivation(null);</span></span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :     emit(NoNeedShowWaitingPopup());</span></span>
<span id="L129"><span class="lineNum">     129</span>              :   }</span>
<span id="L130"><span class="lineNum">     130</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
