<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/feature/activated_pos_limit/3d_secure/activate_pos_limit_three_d_secure_screen.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/feature/activated_pos_limit/3d_secure">lib/feature/activated_pos_limit/3d_secure</a> - activate_pos_limit_three_d_secure_screen.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">67</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter/material.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/base/page_base.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/feature/webview/common_webview_controller.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/feature/webview/webview.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'package:flutter_common_package/global_key_provider.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'package:flutter_common_package/ui_model/error_ui_model.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../../../resources/resources.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../../data/repository/user_repo.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../../prepare_for_app_initiation.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../../util/secure_storage_helper/secure_storage_helper.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../../util/ui_utils/evo_ui_utils.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../../widget/evo_appbar.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../../widget/evo_appbar_leading_button.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../payment/utils/payment_navigate_helper_mixin.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../webview/models/evo_webview_arg.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../activate_card_base/activate_card_state_base.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../card_activation_status_cubit/card_activation_status_cubit.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../card_activation_status_cubit/card_activation_status_handle_utils.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../card_confirm_activation_cubit/card_confirm_activation_cubit.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../models/platform_active_card.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import '../utils/activate_pos_limit_constants.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span>              : class ActivatePosLimitThreeDSecureScreenArg extends PageBaseArg {</span>
<span id="L28"><span class="lineNum">      28</span>              :   final String? url;</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   ActivatePosLimitThreeDSecureScreenArg({</span></span>
<span id="L31"><span class="lineNum">      31</span>              :     required this.url,</span>
<span id="L32"><span class="lineNum">      32</span>              :   });</span>
<span id="L33"><span class="lineNum">      33</span>              : }</span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span>              : class ActivatePosLimitThreeDSecureScreen extends PageBase {</span>
<span id="L36"><span class="lineNum">      36</span>              :   final ActivatePosLimitThreeDSecureScreenArg? arg;</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :   static void pushNamed(String? url) {</span></span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :     return navigatorContext?.pushNamed(</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :       Screen.activatePosLimitThreeDSecureScreen.name,</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :       extra: ActivatePosLimitThreeDSecureScreenArg(url: url),</span></span>
<span id="L42"><span class="lineNum">      42</span>              :     );</span>
<span id="L43"><span class="lineNum">      43</span>              :   }</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :   const ActivatePosLimitThreeDSecureScreen({</span></span>
<span id="L46"><span class="lineNum">      46</span>              :     this.arg,</span>
<span id="L47"><span class="lineNum">      47</span>              :     super.key,</span>
<span id="L48"><span class="lineNum">      48</span>              :   });</span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L51"><span class="lineNum">      51</span>              :   State&lt;ActivatePosLimitThreeDSecureScreen&gt; createState() =&gt;</span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :       ActivatePosLimitThreeDSecureScreenState();</span></span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L55"><span class="lineNum">      55</span>              :   EventTrackingScreenId get eventTrackingScreenId =&gt; EventTrackingScreenId.undefined;</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L58"><span class="lineNum">      58</span>              :   RouteSettings get routeSettings =&gt;</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :       RouteSettings(name: Screen.activatePosLimitThreeDSecureScreen.routeName);</span></span>
<span id="L60"><span class="lineNum">      60</span>              : }</span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span>              : class ActivatePosLimitThreeDSecureScreenState</span>
<span id="L63"><span class="lineNum">      63</span>              :     extends ActivateCardStateBase&lt;ActivatePosLimitThreeDSecureScreen&gt;</span>
<span id="L64"><span class="lineNum">      64</span>              :     with PaymentNavigationHelperMixin {</span>
<span id="L65"><span class="lineNum">      65</span>              :   final CardActivationStatusCubit _cubit = CardActivationStatusCubit(</span>
<span id="L66"><span class="lineNum">      66</span>              :     userRepo: getIt.get&lt;UserRepo&gt;(),</span>
<span id="L67"><span class="lineNum">      67</span>              :   );</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span>              :   final CardConfirmActivationCubit _confirmActivationCubit = CardConfirmActivationCubit(</span>
<span id="L70"><span class="lineNum">      70</span>              :     userRepo: getIt.get&lt;UserRepo&gt;(),</span>
<span id="L71"><span class="lineNum">      71</span>              :   );</span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span>              :   CommonWebViewController? webViewController;</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L76"><span class="lineNum">      76</span>              :   void initState() {</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     super.initState();</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :     webViewController = CommonWebViewController(</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :       onRedirectUrl: (Uri? uri, _) {</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :         handleOnRedirectUrl(uri);</span></span>
<span id="L81"><span class="lineNum">      81</span>              :       },</span>
<span id="L82"><span class="lineNum">      82</span>              :     );</span>
<span id="L83"><span class="lineNum">      83</span>              :   }</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L86"><span class="lineNum">      86</span>              :   void handleBackButton() {</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :     showDialogConfirmCancel3DSActivatePosLimit(</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :       onClickPositive: () {</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :         EvoUiUtils().hideHudLoading();</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :         navigatorContext?.popUntilNamed(Screen.activatePosLimitScreen.name);</span></span>
<span id="L91"><span class="lineNum">      91</span>              :       },</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :       onClickNegative: () {</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :         navigatorContext?.pop();</span></span>
<span id="L94"><span class="lineNum">      94</span>              :       },</span>
<span id="L95"><span class="lineNum">      95</span>              :     );</span>
<span id="L96"><span class="lineNum">      96</span>              :   }</span>
<span id="L97"><span class="lineNum">      97</span>              : </span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L99"><span class="lineNum">      99</span>              :   bool get enableAppBar =&gt; false;</span>
<span id="L100"><span class="lineNum">     100</span>              : </span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L102"><span class="lineNum">     102</span>              :   Widget buildBody(BuildContext context) {</span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :     return MultiBlocProvider(</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :       providers: &lt;BlocProvider&lt;dynamic&gt;&gt;[</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :         BlocProvider&lt;CardActivationStatusCubit&gt;(</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :           create: (_) =&gt; _cubit,</span></span>
<span id="L107"><span class="lineNum">     107</span>              :         ),</span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :         BlocProvider&lt;CardConfirmActivationCubit&gt;(</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :           create: (_) =&gt; _confirmActivationCubit,</span></span>
<span id="L110"><span class="lineNum">     110</span>              :         ),</span>
<span id="L111"><span class="lineNum">     111</span>              :       ],</span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :       child: MultiBlocListener(</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :         listeners: &lt;BlocListener&lt;dynamic, dynamic&gt;&gt;[</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :           BlocListener&lt;CardActivationStatusCubit, CardActivationStatusState&gt;(</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :             listener: (_, CardActivationStatusState state) {</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :               CardActivationStatusHandleUtils().handleCardStatusStateChange(</span></span>
<span id="L117"><span class="lineNum">     117</span>              :                 state: state,</span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :                 onCardStatusActivated: () =&gt;</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :                     _confirmActivationCubit.confirmActivationCard(platform: PlatformActiveCard.evo),</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :                 onGetCardStatusError: (ErrorUIModel error) =&gt; handleEvoApiError(error),</span></span>
<span id="L121"><span class="lineNum">     121</span>              :               );</span>
<span id="L122"><span class="lineNum">     122</span>              :             },</span>
<span id="L123"><span class="lineNum">     123</span>              :           ),</span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :           BlocListener&lt;CardConfirmActivationCubit, CardConfirmActivationState&gt;(</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :             listener: (_, CardConfirmActivationState state) {</span></span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :               _handleConfirmActivation(state);</span></span>
<span id="L127"><span class="lineNum">     127</span>              :             },</span>
<span id="L128"><span class="lineNum">     128</span>              :           ),</span>
<span id="L129"><span class="lineNum">     129</span>              :         ],</span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :         child: CommonWebView(</span></span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :           arg: EvoWebViewArg(</span></span>
<span id="L132"><span class="lineNum">     132</span>              :             title: '',</span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :             url: widget.arg?.url,</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :             appBar: EvoAppBar(</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :               leading: EvoAppBarLeadingButton(</span></span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :                 onPressed: () {</span></span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaUNC">           0 :                   handleBackButton();</span></span>
<span id="L138"><span class="lineNum">     138</span>              :                 },</span>
<span id="L139"><span class="lineNum">     139</span>              :               ),</span>
<span id="L140"><span class="lineNum">     140</span>              :             ),</span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaUNC">           0 :             controller: webViewController,</span></span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :             errorWidget: (_, __) =&gt; const SizedBox.shrink(),</span></span>
<span id="L143"><span class="lineNum">     143</span>              :           ),</span>
<span id="L144"><span class="lineNum">     144</span>              :         ),</span>
<span id="L145"><span class="lineNum">     145</span>              :       ),</span>
<span id="L146"><span class="lineNum">     146</span>              :     );</span>
<span id="L147"><span class="lineNum">     147</span>              :   }</span>
<span id="L148"><span class="lineNum">     148</span>              : </span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; handleOnRedirectUrl(Uri? uri) async {</span></span>
<span id="L150"><span class="lineNum">     150</span>              :     final bool isOTPInputtingDone =</span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :         uri.toString().startsWith(ActivatePosLimitConstants.threeDSecureCallbackUrl);</span></span>
<span id="L152"><span class="lineNum">     152</span>              : </span>
<span id="L153"><span class="lineNum">     153</span>              :     if (!isOTPInputtingDone) {</span>
<span id="L154"><span class="lineNum">     154</span>              :       return;</span>
<span id="L155"><span class="lineNum">     155</span>              :     }</span>
<span id="L156"><span class="lineNum">     156</span>              : </span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :     EvoUiUtils().showHudLoading();</span></span>
<span id="L158"><span class="lineNum">     158</span>              : </span>
<span id="L159"><span class="lineNum">     159</span>              :     /// Save current time to local storage</span>
<span id="L160"><span class="lineNum">     160</span>              :     /// Check and show bottom sheet waiting in confirm and pay screen</span>
<span id="L161"><span class="lineNum">     161</span>              :     /// To avoid issue send TPB OTP two times in 60s</span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaUNC">           0 :     getIt</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :         .get&lt;EvoLocalStorageHelper&gt;()</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :         .setLastTimeRequest3DSCardActivation(DateTime.now().millisecondsSinceEpoch);</span></span>
<span id="L165"><span class="lineNum">     165</span>              : </span>
<span id="L166"><span class="lineNum">     166</span>              :     /// Wait 30s before check card status</span>
<span id="L167"><span class="lineNum">     167</span>              :     /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3787620432/ES+CO+Dive-in+Card+activation+within+payment+flow#TPB-redirects-to-the-EVO-app.</span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaUNC">           0 :     await Future&lt;void&gt;.delayed(Duration(seconds: 30));</span></span>
<span id="L169"><span class="lineNum">     169</span>              : </span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaUNC">           0 :     _cubit.getCardActivationStatus();</span></span>
<span id="L171"><span class="lineNum">     171</span>              :   }</span>
<span id="L172"><span class="lineNum">     172</span>              : </span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaUNC">           0 :   void _handleConfirmActivation(CardConfirmActivationState state) {</span></span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaUNC">           0 :     if (state is CardConfirmActivationLoadingState) {</span></span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :       EvoUiUtils().showHudLoading();</span></span>
<span id="L176"><span class="lineNum">     176</span>              :       return;</span>
<span id="L177"><span class="lineNum">     177</span>              :     }</span>
<span id="L178"><span class="lineNum">     178</span>              : </span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :     EvoUiUtils().hideHudLoading();</span></span>
<span id="L180"><span class="lineNum">     180</span>              : </span>
<span id="L181"><span class="lineNum">     181</span> <span class="tlaUNC">           0 :     if (state is CardConfirmActivationSucceedState) {</span></span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :       activatedPOSLimitFlow.onSuccess(context: context);</span></span>
<span id="L183"><span class="lineNum">     183</span>              :       return;</span>
<span id="L184"><span class="lineNum">     184</span>              :     }</span>
<span id="L185"><span class="lineNum">     185</span>              : </span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaUNC">           0 :     if (state is CardConfirmActivationFailureState) {</span></span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaUNC">           0 :       handleEvoApiError(state.errorUIModel);</span></span>
<span id="L188"><span class="lineNum">     188</span>              :       return;</span>
<span id="L189"><span class="lineNum">     189</span>              :     }</span>
<span id="L190"><span class="lineNum">     190</span>              :   }</span>
<span id="L191"><span class="lineNum">     191</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
