<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/sign_in_otp_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - sign_in_otp_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">37</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import 'action_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'ekyc_info_entity.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : class SignInOtpEntity extends BaseEntity {</span>
<span id="L9"><span class="lineNum">       9</span>              :   ///after received this request, server will:</span>
<span id="L10"><span class="lineNum">      10</span>              :   ///if OTP is valid, return status code [CommonHttpClient.SUCCESS], verdict [verdictSuccess]</span>
<span id="L11"><span class="lineNum">      11</span>              :   static const String verdictSuccess = 'success';</span>
<span id="L12"><span class="lineNum">      12</span>              : </span>
<span id="L13"><span class="lineNum">      13</span>              :   ///if OTP is invalid, return status code [CommonHttpClient.BAD_REQUEST], verdict [verdictIncorrectOtp]</span>
<span id="L14"><span class="lineNum">      14</span>              :   static const String verdictIncorrectOtp = 'incorrect_otp';</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span>              :   /// if incorrect otp too many times, return status code [CommonHttpClient.LIMIT_EXCEEDED]</span>
<span id="L17"><span class="lineNum">      17</span>              :   static const String verdictLimitExceeded = 'limit_exceeded';</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              :   //if OTP is expired, return status code [CommonHttpClient.BAD_REQUEST], verdict [expiredData]</span>
<span id="L20"><span class="lineNum">      20</span>              :   static const String verdictExpiredOTP = 'expired_data';</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              :   static const String verdictUserNotExisted = 'record_not_found';</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span>              :   static const String verdictInvalidDeviceToken = 'invalid_device_token';</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   // If user has any ongoing session prior to entering Native DOP flow</span>
<span id="L27"><span class="lineNum">      27</span>              :   static const String verdictAlreadySignedIn = 'already_signed_in';</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              :   /// Response with status code = [CommonHttpClient.BAD_REQUEST], verdict = [token_mismatch]</span>
<span id="L30"><span class="lineNum">      30</span>              :   /// If user has any ongoing session prior to entering Native DOP flow but with other phone</span>
<span id="L31"><span class="lineNum">      31</span>              :   /// https://trustingsocial1.atlassian.net/browse/EMA-4828</span>
<span id="L32"><span class="lineNum">      32</span>              :   static const String verdictCompleteWithOtherPhone = 'token_mismatch';</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span>              :   final String? accessToken;</span>
<span id="L35"><span class="lineNum">      35</span>              :   final String? sessionToken;</span>
<span id="L36"><span class="lineNum">      36</span>              :   final String? refreshToken;</span>
<span id="L37"><span class="lineNum">      37</span>              :   final String? biometricToken;</span>
<span id="L38"><span class="lineNum">      38</span>              :   final String? deviceToken;</span>
<span id="L39"><span class="lineNum">      39</span>              :   final String? notificationAuthKey;</span>
<span id="L40"><span class="lineNum">      40</span>              :   final ActionEntity? action;</span>
<span id="L41"><span class="lineNum">      41</span>              :   final String? challengeType;</span>
<span id="L42"><span class="lineNum">      42</span>              :   final int? userId;</span>
<span id="L43"><span class="lineNum">      43</span>              :   final String? status;</span>
<span id="L44"><span class="lineNum">      44</span>              :   final EKYCSessionEntity? ekycCredential;</span>
<span id="L45"><span class="lineNum">      45</span>              :   final String? authNotiToken;</span>
<span id="L46"><span class="lineNum">      46</span>              :   final int? remainAttempt;</span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :   SignInOtpEntity({</span></span>
<span id="L49"><span class="lineNum">      49</span>              :     this.accessToken,</span>
<span id="L50"><span class="lineNum">      50</span>              :     this.sessionToken,</span>
<span id="L51"><span class="lineNum">      51</span>              :     this.refreshToken,</span>
<span id="L52"><span class="lineNum">      52</span>              :     this.biometricToken,</span>
<span id="L53"><span class="lineNum">      53</span>              :     this.deviceToken,</span>
<span id="L54"><span class="lineNum">      54</span>              :     this.notificationAuthKey,</span>
<span id="L55"><span class="lineNum">      55</span>              :     this.action,</span>
<span id="L56"><span class="lineNum">      56</span>              :     this.challengeType,</span>
<span id="L57"><span class="lineNum">      57</span>              :     this.userId,</span>
<span id="L58"><span class="lineNum">      58</span>              :     this.status,</span>
<span id="L59"><span class="lineNum">      59</span>              :     this.ekycCredential,</span>
<span id="L60"><span class="lineNum">      60</span>              :     this.authNotiToken,</span>
<span id="L61"><span class="lineNum">      61</span>              :     this.remainAttempt,</span>
<span id="L62"><span class="lineNum">      62</span>              :   });</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :   SignInOtpEntity.unserializable()</span></span>
<span id="L65"><span class="lineNum">      65</span>              :       : accessToken = null,</span>
<span id="L66"><span class="lineNum">      66</span>              :         sessionToken = null,</span>
<span id="L67"><span class="lineNum">      67</span>              :         refreshToken = null,</span>
<span id="L68"><span class="lineNum">      68</span>              :         biometricToken = null,</span>
<span id="L69"><span class="lineNum">      69</span>              :         deviceToken = null,</span>
<span id="L70"><span class="lineNum">      70</span>              :         userId = null,</span>
<span id="L71"><span class="lineNum">      71</span>              :         action = null,</span>
<span id="L72"><span class="lineNum">      72</span>              :         challengeType = null,</span>
<span id="L73"><span class="lineNum">      73</span>              :         notificationAuthKey = null,</span>
<span id="L74"><span class="lineNum">      74</span>              :         status = null,</span>
<span id="L75"><span class="lineNum">      75</span>              :         ekycCredential = null,</span>
<span id="L76"><span class="lineNum">      76</span>              :         authNotiToken = null,</span>
<span id="L77"><span class="lineNum">      77</span>              :         remainAttempt = null,</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L79"><span class="lineNum">      79</span>              : </span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :   SignInOtpEntity.fromBaseResponse(BaseResponse super.baseResponse)</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :       : accessToken = baseResponse.data?['access_token'] as String?,</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :         refreshToken = baseResponse.data?['refresh_token'] as String?,</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :         biometricToken = baseResponse.data?['biometric_token'] as String?,</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :         deviceToken = baseResponse.data?['device_token'] as String?,</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :         sessionToken = baseResponse.data?['session_token'] as String?,</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :         action = (baseResponse.data?['action'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :             ? ActionEntity.fromJson(baseResponse.data?['action'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L88"><span class="lineNum">      88</span>              :             : null,</span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :         challengeType = baseResponse.data?['challenge_type'] as String?,</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :         userId = baseResponse.data?['user_id'] as int?,</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :         notificationAuthKey = baseResponse.data?['notification_auth_key'] as String?,</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :         status = baseResponse.data?['status'] as String?,</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :         ekycCredential = (baseResponse.data?['ekyc_credential'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :             ? EKYCSessionEntity.fromBaseResponseForFaceOtp(</span></span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :                 baseResponse, baseResponse.data?['ekyc_credential'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L96"><span class="lineNum">      96</span>              :             : null,</span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :         authNotiToken = baseResponse.data?['auth_noti_token'] as String?,</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :         remainAttempt = baseResponse.data?['remain_attempt'] as int?,</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L100"><span class="lineNum">     100</span>              : </span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L102"><span class="lineNum">     102</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :     json.addAll(&lt;String, dynamic&gt;{</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :       'access_token': accessToken,</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :       'refresh_token': refreshToken,</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :       'biometric_token': biometricToken,</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :       'device_token': deviceToken,</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :       'session_token': sessionToken,</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :       'action': action,</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :       'challenge_type': challengeType,</span></span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :       'user_id': userId,</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :       'notification_auth_key': notificationAuthKey,</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :       'status': status,</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :       'ekyc_credential': ekycCredential?.toJson(),</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :       'auth_noti_token': authNotiToken,</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :       'remain_attempt': remainAttempt,</span></span>
<span id="L118"><span class="lineNum">     118</span>              :     });</span>
<span id="L119"><span class="lineNum">     119</span>              :     return json;</span>
<span id="L120"><span class="lineNum">     120</span>              :   }</span>
<span id="L121"><span class="lineNum">     121</span>              : }</span>
<span id="L122"><span class="lineNum">     122</span>              : </span>
<span id="L123"><span class="lineNum">     123</span>              : enum SignInChallengeType {</span>
<span id="L124"><span class="lineNum">     124</span>              :   action('action'),</span>
<span id="L125"><span class="lineNum">     125</span>              :   verifyPin('verify_pin'),</span>
<span id="L126"><span class="lineNum">     126</span>              :   createPin('create_pin'),</span>
<span id="L127"><span class="lineNum">     127</span>              :   faceOTP('face_otp'),</span>
<span id="L128"><span class="lineNum">     128</span>              :   faceAuth('face_auth');</span>
<span id="L129"><span class="lineNum">     129</span>              : </span>
<span id="L130"><span class="lineNum">     130</span>              :   const SignInChallengeType(this.value);</span>
<span id="L131"><span class="lineNum">     131</span>              : </span>
<span id="L132"><span class="lineNum">     132</span>              :   final String value;</span>
<span id="L133"><span class="lineNum">     133</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
