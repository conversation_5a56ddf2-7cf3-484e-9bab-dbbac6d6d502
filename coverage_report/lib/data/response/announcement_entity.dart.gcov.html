<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/announcement_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - announcement_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">35</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/util/functions.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'action_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : class AnnouncementEntity {</span>
<span id="L6"><span class="lineNum">       6</span>              :   static const String statusRead = 'read';</span>
<span id="L7"><span class="lineNum">       7</span>              :   static const String statusUnread = 'unread';</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              :   final ActionEntity? action;</span>
<span id="L10"><span class="lineNum">      10</span>              :   final String? image;</span>
<span id="L11"><span class="lineNum">      11</span>              :   final int id;</span>
<span id="L12"><span class="lineNum">      12</span>              :   final String? description;</span>
<span id="L13"><span class="lineNum">      13</span>              :   final String? title;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final String? createdAt;</span>
<span id="L15"><span class="lineNum">      15</span>              :   final String? sentAt;</span>
<span id="L16"><span class="lineNum">      16</span>              :   final String? status;</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaUNC">           0 :   AnnouncementEntity(</span></span>
<span id="L19"><span class="lineNum">      19</span>              :       {required this.id,</span>
<span id="L20"><span class="lineNum">      20</span>              :       this.action,</span>
<span id="L21"><span class="lineNum">      21</span>              :       this.image,</span>
<span id="L22"><span class="lineNum">      22</span>              :       this.description,</span>
<span id="L23"><span class="lineNum">      23</span>              :       this.title,</span>
<span id="L24"><span class="lineNum">      24</span>              :       this.createdAt,</span>
<span id="L25"><span class="lineNum">      25</span>              :       this.sentAt,</span>
<span id="L26"><span class="lineNum">      26</span>              :       this.status});</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :   DateTime? get sentAtDateTime =&gt; commonUtilFunction.toDateTime(sentAt);</span></span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   AnnouncementEntity.fromJson(Map&lt;String, dynamic&gt; json)</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :       : action = (json['action'] as Map&lt;dynamic, dynamic&gt;?) != null</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :             ? ActionEntity.fromJson(json['action'] as Map&lt;dynamic, dynamic&gt;)</span></span>
<span id="L33"><span class="lineNum">      33</span>              :             : null,</span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :         image = json['image'] as String?,</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :         id = json['id'] as int,</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :         description = json['description'] as String?,</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :         title = json['title'] as String?,</span></span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :         createdAt = json['created_at'] as String?,</span></span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :         sentAt = json['sent_at'] as String?,</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :         status = json['status'] as String?;</span></span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; &lt;String, dynamic&gt;{</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :         'action': action?.toJson(),</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :         'image': image,</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :         'id': id,</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :         'description': description,</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :         'title': title,</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :         'created_at': createdAt,</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :         'sent_at': sentAt,</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :         'status': status,</span></span>
<span id="L51"><span class="lineNum">      51</span>              :       };</span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :   AnnouncementEntity copyWith({</span></span>
<span id="L54"><span class="lineNum">      54</span>              :     ActionEntity? action,</span>
<span id="L55"><span class="lineNum">      55</span>              :     String? createdAt,</span>
<span id="L56"><span class="lineNum">      56</span>              :     String? sentAt,</span>
<span id="L57"><span class="lineNum">      57</span>              :     int? id,</span>
<span id="L58"><span class="lineNum">      58</span>              :     String? settings,</span>
<span id="L59"><span class="lineNum">      59</span>              :     String? status,</span>
<span id="L60"><span class="lineNum">      60</span>              :     String? image,</span>
<span id="L61"><span class="lineNum">      61</span>              :     String? description,</span>
<span id="L62"><span class="lineNum">      62</span>              :     String? title,</span>
<span id="L63"><span class="lineNum">      63</span>              :   }) =&gt;</span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :       AnnouncementEntity(</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :         action: action ?? this.action,</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :         createdAt: createdAt ?? this.createdAt,</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :         sentAt: sentAt ?? this.sentAt,</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :         id: id ?? this.id,</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :         status: status ?? this.status,</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :         image: image ?? this.image,</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :         description: description ?? this.description,</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :         title: title ?? this.title,</span></span>
<span id="L73"><span class="lineNum">      73</span>              :       );</span>
<span id="L74"><span class="lineNum">      74</span>              : }</span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span>              : extension AnnouncementExt on AnnouncementEntity {</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :   bool hasAction() {</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :     return action != null;</span></span>
<span id="L79"><span class="lineNum">      79</span>              :   }</span>
<span id="L80"><span class="lineNum">      80</span>              : </span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :   bool isUnread() {</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :     return status == AnnouncementEntity.statusUnread;</span></span>
<span id="L83"><span class="lineNum">      83</span>              :   }</span>
<span id="L84"><span class="lineNum">      84</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
