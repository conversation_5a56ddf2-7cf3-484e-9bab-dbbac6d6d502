<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/credit_limit_widget_config_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - credit_limit_widget_config_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">14</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : enum CreditStatus {</span>
<span id="L2"><span class="lineNum">       2</span>              :   waitingForApproval('waiting_for_approval'),</span>
<span id="L3"><span class="lineNum">       3</span>              :   notReadyForPayment('not_ready_for_payment'),</span>
<span id="L4"><span class="lineNum">       4</span>              :   readyForPayment('ready_for_payment'),</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              :   /// The status is returned when the DOP State as successful more than 90 days.</span>
<span id="L7"><span class="lineNum">       7</span>              :   /// After 90 Days, BE will not call to DOP again to get the latest status.</span>
<span id="L8"><span class="lineNum">       8</span>              :   outOfSyncWithDOP('out_of_sync');</span>
<span id="L9"><span class="lineNum">       9</span>              : </span>
<span id="L10"><span class="lineNum">      10</span>              :   final String value;</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              :   const CreditStatus(this.value);</span>
<span id="L13"><span class="lineNum">      13</span>              : }</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span>              : class CreditLimitWidgetConfigEntity {</span>
<span id="L16"><span class="lineNum">      16</span>              :   final bool? display;</span>
<span id="L17"><span class="lineNum">      17</span>              :   final int? creditLimit;</span>
<span id="L18"><span class="lineNum">      18</span>              :   final String? creditStatus;</span>
<span id="L19"><span class="lineNum">      19</span>              : </span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaUNC">           0 :   CreditLimitWidgetConfigEntity({</span></span>
<span id="L21"><span class="lineNum">      21</span>              :     this.display,</span>
<span id="L22"><span class="lineNum">      22</span>              :     this.creditLimit,</span>
<span id="L23"><span class="lineNum">      23</span>              :     this.creditStatus,</span>
<span id="L24"><span class="lineNum">      24</span>              :   });</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaUNC">           0 :   CreditLimitWidgetConfigEntity.fromJson(Map&lt;String, dynamic&gt; json)</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :       : display = json['display'] as bool?,</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :         creditLimit = json['credit_limit'] as int?,</span></span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaUNC">           0 :         creditStatus = json['status'] as String?;</span></span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; &lt;String, dynamic&gt;{</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :         'display': display,</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :         'credit_limit': creditLimit,</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :         'status': creditStatus,</span></span>
<span id="L35"><span class="lineNum">      35</span>              :       };</span>
<span id="L36"><span class="lineNum">      36</span>              : }</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span>              : extension CreditLimitWidgetConfigEntityExt on CreditLimitWidgetConfigEntity {</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :   bool get isCardApprovedOrOutOfSync =&gt;</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :       isCardReadyForPayment || isCardNotReadyForPayment || isOutOfSyncWithDOP;</span></span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :   bool get isCardReadyForPayment =&gt; creditStatus == CreditStatus.readyForPayment.value;</span></span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :   bool get isOutOfSyncWithDOP =&gt; creditStatus == CreditStatus.outOfSyncWithDOP.value;</span></span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :   bool get isCardNotReadyForPayment =&gt; creditStatus == CreditStatus.notReadyForPayment.value;</span></span>
<span id="L47"><span class="lineNum">      47</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
