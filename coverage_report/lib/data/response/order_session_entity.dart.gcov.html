<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/order_session_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - order_session_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">55</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'action_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'emi_offer_entity.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'merchant_info_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'order_info_entity.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'payment_info_entity.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'promotion_info_entity.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import 'store_info_entity.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import 'user_payment_information_entity.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              : class OrderSessionEntity {</span>
<span id="L13"><span class="lineNum">      13</span>              :   final String? id;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final String? transactionId;</span>
<span id="L15"><span class="lineNum">      15</span>              :   final String? channel;</span>
<span id="L16"><span class="lineNum">      16</span>              :   final String? createdAt;</span>
<span id="L17"><span class="lineNum">      17</span>              :   final String? productCode;</span>
<span id="L18"><span class="lineNum">      18</span>              :   final List&lt;String?&gt;? promotionCodes;</span>
<span id="L19"><span class="lineNum">      19</span>              :   final String? status;</span>
<span id="L20"><span class="lineNum">      20</span>              :   final String? updatedAt;</span>
<span id="L21"><span class="lineNum">      21</span>              :   final StoreInfoEntity? storeInfo;</span>
<span id="L22"><span class="lineNum">      22</span>              :   final ActionEntity? nextAction;</span>
<span id="L23"><span class="lineNum">      23</span>              :   final OrderInfoEntity? orderInfo;</span>
<span id="L24"><span class="lineNum">      24</span>              :   final PaymentInfoEntity? paymentInfo;</span>
<span id="L25"><span class="lineNum">      25</span>              :   final PromotionInfoEntity? promotionInfo;</span>
<span id="L26"><span class="lineNum">      26</span>              :   final UserPaymentInformationEntity? userInfo;</span>
<span id="L27"><span class="lineNum">      27</span>              :   final int? fee;</span>
<span id="L28"><span class="lineNum">      28</span>              :   final int? orderAmount;</span>
<span id="L29"><span class="lineNum">      29</span>              :   final int? promotionAmount;</span>
<span id="L30"><span class="lineNum">      30</span>              :   final int? userChargeAmount;</span>
<span id="L31"><span class="lineNum">      31</span>              :   final MerchantInfoEntity? merchantInfo;</span>
<span id="L32"><span class="lineNum">      32</span>              :   final EmiOfferEntity? emiOffer;</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :   OrderSessionEntity({</span></span>
<span id="L35"><span class="lineNum">      35</span>              :     this.id,</span>
<span id="L36"><span class="lineNum">      36</span>              :     this.transactionId,</span>
<span id="L37"><span class="lineNum">      37</span>              :     this.fee,</span>
<span id="L38"><span class="lineNum">      38</span>              :     this.orderAmount,</span>
<span id="L39"><span class="lineNum">      39</span>              :     this.promotionAmount,</span>
<span id="L40"><span class="lineNum">      40</span>              :     this.userChargeAmount,</span>
<span id="L41"><span class="lineNum">      41</span>              :     this.channel,</span>
<span id="L42"><span class="lineNum">      42</span>              :     this.createdAt,</span>
<span id="L43"><span class="lineNum">      43</span>              :     this.productCode,</span>
<span id="L44"><span class="lineNum">      44</span>              :     this.promotionCodes,</span>
<span id="L45"><span class="lineNum">      45</span>              :     this.status,</span>
<span id="L46"><span class="lineNum">      46</span>              :     this.updatedAt,</span>
<span id="L47"><span class="lineNum">      47</span>              :     this.storeInfo,</span>
<span id="L48"><span class="lineNum">      48</span>              :     this.promotionInfo,</span>
<span id="L49"><span class="lineNum">      49</span>              :     this.nextAction,</span>
<span id="L50"><span class="lineNum">      50</span>              :     this.orderInfo,</span>
<span id="L51"><span class="lineNum">      51</span>              :     this.paymentInfo,</span>
<span id="L52"><span class="lineNum">      52</span>              :     this.userInfo,</span>
<span id="L53"><span class="lineNum">      53</span>              :     this.merchantInfo,</span>
<span id="L54"><span class="lineNum">      54</span>              :     this.emiOffer,</span>
<span id="L55"><span class="lineNum">      55</span>              :   });</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :   DateTime? get createdAtDateTime =&gt; commonUtilFunction.toDateTime(createdAt);</span></span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :   factory OrderSessionEntity.fromJson(Map&lt;String, dynamic&gt; json) =&gt; OrderSessionEntity(</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :         id: json['id'] as String?,</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :         transactionId: json['transaction_id'] as String?,</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :         channel: json['channel'] as String?,</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :         fee: json['fee'] as int?,</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :         orderAmount: json['order_amount'] as int?,</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :         promotionAmount: json['promotion_amount'] as int?,</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :         userChargeAmount: json['user_charge_amount'] as int?,</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :         createdAt: json['created_at'] as String?,</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :         productCode: json['product_code'] as String?,</span></span>
<span id="L69"><span class="lineNum">      69</span>              :         promotionCodes:</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :             (json['promotion_codes'] as List&lt;dynamic&gt;?)?.map((dynamic e) =&gt; e.toString()).toList(),</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :         status: json['status'] as String?,</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :         updatedAt: json['updated_at'] as String?,</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :         storeInfo: (json['store_info'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :             ? StoreInfoEntity.fromJson(json['store_info'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L75"><span class="lineNum">      75</span>              :             : null,</span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :         promotionInfo: (json['promotion_info'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :             ? PromotionInfoEntity.fromJson(</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :                 json['promotion_info'] as Map&lt;String, dynamic&gt;,</span></span>
<span id="L79"><span class="lineNum">      79</span>              :               )</span>
<span id="L80"><span class="lineNum">      80</span>              :             : null,</span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :         nextAction: (json['next_action'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :             ? ActionEntity.fromJson(</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :                 json['next_action'] as Map&lt;String, dynamic&gt;,</span></span>
<span id="L84"><span class="lineNum">      84</span>              :               )</span>
<span id="L85"><span class="lineNum">      85</span>              :             : null,</span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :         orderInfo: (json['order_info'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :             ? OrderInfoEntity.fromJson(json['order_info'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L88"><span class="lineNum">      88</span>              :             : null,</span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :         paymentInfo: (json['payment_info'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :             ? PaymentInfoEntity.fromJson(json['payment_info'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L91"><span class="lineNum">      91</span>              :             : null,</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :         userInfo: (json['user_info'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :             ? UserPaymentInformationEntity.fromJson(</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :                 json['user_info'] as Map&lt;String, dynamic&gt;,</span></span>
<span id="L95"><span class="lineNum">      95</span>              :               )</span>
<span id="L96"><span class="lineNum">      96</span>              :             : null,</span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :         merchantInfo: (json['merchant_info'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :             ? MerchantInfoEntity.fromJson(json['merchant_info'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L99"><span class="lineNum">      99</span>              :             : null,</span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :         emiOffer: (json['emi_offer'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :             ? EmiOfferEntity.fromJson(json['emi_offer'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L102"><span class="lineNum">     102</span>              :             : null,</span>
<span id="L103"><span class="lineNum">     103</span>              :       );</span>
<span id="L104"><span class="lineNum">     104</span>              : </span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; &lt;String, dynamic&gt;{</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :         'id': id,</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :         'transaction_id': transactionId,</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :         'fee': fee,</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :         'order_amount': orderAmount,</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :         'promotion_amount': promotionAmount,</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :         'user_charge_amount': userChargeAmount,</span></span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :         'channel': channel,</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :         'created_at': createdAt,</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :         'product_code': productCode,</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :         'promotion_codes': promotionCodes,</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :         'status': status,</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :         'updated_at': updatedAt,</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :         'store_info': storeInfo?.toJson(),</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :         'promotion_info': promotionInfo?.toJson(),</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :         'next_action': nextAction?.toJson(),</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :         'order_info': orderInfo?.toJson(),</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :         'payment_info': paymentInfo?.toJson(),</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :         'user_info': userInfo?.toJson(),</span></span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :         'merchant_info': merchantInfo?.toJson(),</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :         'emi_offer': emiOffer?.toJson(),</span></span>
<span id="L126"><span class="lineNum">     126</span>              :       };</span>
<span id="L127"><span class="lineNum">     127</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
