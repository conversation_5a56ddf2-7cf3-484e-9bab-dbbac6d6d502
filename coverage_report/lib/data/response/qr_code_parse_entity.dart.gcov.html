<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/qr_code_parse_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - qr_code_parse_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">23</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import 'vn_pay_qr_info_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : /// Refer to: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3654778899/ES+CO+Payment+VNPay+QR#API-Spec</span>
<span id="L8"><span class="lineNum">       8</span>              : class QrCodeParseEntity extends BaseEntity {</span>
<span id="L9"><span class="lineNum">       9</span>              :   /// Status code [CommonHttpClient.BAD_REQUEST]</span>
<span id="L10"><span class="lineNum">      10</span>              :   static const String verdictInvalidQRFormat = 'invalid_qr_format';</span>
<span id="L11"><span class="lineNum">      11</span>              :   static const String verdictOrderNotFound = 'order_not_found';</span>
<span id="L12"><span class="lineNum">      12</span>              :   static const String verdictInvalidOrderStatus = 'invalid_order_status';</span>
<span id="L13"><span class="lineNum">      13</span>              :   static const String verdictInvalidProductCode = 'invalid_product_code';</span>
<span id="L14"><span class="lineNum">      14</span>              :   static const String verdictOrderExpired = 'order_expired';</span>
<span id="L15"><span class="lineNum">      15</span>              :   static const String verdictOrderInPayment = 'order_in_payment';</span>
<span id="L16"><span class="lineNum">      16</span>              :   static const String verdictPayTimesExceeded = 'pay_times_exceeded';</span>
<span id="L17"><span class="lineNum">      17</span>              :   static const String verdictStoreInactive = 'store_inactive';</span>
<span id="L18"><span class="lineNum">      18</span>              :   static const String verdictMerchantInactive = 'merchant_inactive';</span>
<span id="L19"><span class="lineNum">      19</span>              :   static const String verdictStoreNotFound = 'store_not_found';</span>
<span id="L20"><span class="lineNum">      20</span>              :   static const String verdictOrderSucceeded = 'order_succeeded';</span>
<span id="L21"><span class="lineNum">      21</span>              :   static const String verdictOrderFailed = 'order_failed';</span>
<span id="L22"><span class="lineNum">      22</span>              :   static const String verdictOrderPending = 'order_requires_manual';</span>
<span id="L23"><span class="lineNum">      23</span>              :   static const String verdictOrderCancelled = 'order_cancelled';</span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span>              :   final String? merchantId;</span>
<span id="L26"><span class="lineNum">      26</span>              :   final String? storeId;</span>
<span id="L27"><span class="lineNum">      27</span>              :   final String? productCode;</span>
<span id="L28"><span class="lineNum">      28</span>              :   final String? type;</span>
<span id="L29"><span class="lineNum">      29</span>              :   final String? orderId;</span>
<span id="L30"><span class="lineNum">      30</span>              :   final VNPayQrInfoEntity? vnPayQrInfo;</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :   QrCodeParseEntity({</span></span>
<span id="L33"><span class="lineNum">      33</span>              :     this.type,</span>
<span id="L34"><span class="lineNum">      34</span>              :     this.productCode,</span>
<span id="L35"><span class="lineNum">      35</span>              :     this.storeId,</span>
<span id="L36"><span class="lineNum">      36</span>              :     this.merchantId,</span>
<span id="L37"><span class="lineNum">      37</span>              :     this.orderId,</span>
<span id="L38"><span class="lineNum">      38</span>              :     this.vnPayQrInfo,</span>
<span id="L39"><span class="lineNum">      39</span>              :   });</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :   QrCodeParseEntity.unserializable()</span></span>
<span id="L42"><span class="lineNum">      42</span>              :       : merchantId = null,</span>
<span id="L43"><span class="lineNum">      43</span>              :         storeId = null,</span>
<span id="L44"><span class="lineNum">      44</span>              :         productCode = null,</span>
<span id="L45"><span class="lineNum">      45</span>              :         type = null,</span>
<span id="L46"><span class="lineNum">      46</span>              :         orderId = null,</span>
<span id="L47"><span class="lineNum">      47</span>              :         vnPayQrInfo = null,</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L49"><span class="lineNum">      49</span>              : </span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :   QrCodeParseEntity.fromBaseResponse(BaseResponse super.response)</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :       : merchantId = response.data?['merchant_id'] as String?,</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :         storeId = response.data?['store_id'] as String?,</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :         productCode = response.data?['product_code'] as String?,</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :         orderId = response.data?['order_id'] as String?,</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :         type = response.data?['type'] as String?,</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :         vnPayQrInfo = (response.data?['vnpay_qr_info'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :             ? VNPayQrInfoEntity.fromJson(response.data?['vnpay_qr_info'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L58"><span class="lineNum">      58</span>              :             : null,</span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L60"><span class="lineNum">      60</span>              : </span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L62"><span class="lineNum">      62</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :     json.addAll(&lt;String, dynamic&gt;{</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :       'merchant_id': merchantId,</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :       'store_id': storeId,</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :       'product_code': productCode,</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :       'order_id': orderId,</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :       'type': type,</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :       'vnpay_qr_info': vnPayQrInfo?.toJson(),</span></span>
<span id="L71"><span class="lineNum">      71</span>              :     });</span>
<span id="L72"><span class="lineNum">      72</span>              :     return json;</span>
<span id="L73"><span class="lineNum">      73</span>              :   }</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L76"><span class="lineNum">      76</span>              :   String toString() {</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     return 'QrCodeParseEntity{merchantId: $merchantId, storeId: $storeId, productCode: $productCode, orderId: $orderId, type: $type, vnPayQrInfo: ${vnPayQrInfo.toString()}}';</span></span>
<span id="L78"><span class="lineNum">      78</span>              :   }</span>
<span id="L79"><span class="lineNum">      79</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
