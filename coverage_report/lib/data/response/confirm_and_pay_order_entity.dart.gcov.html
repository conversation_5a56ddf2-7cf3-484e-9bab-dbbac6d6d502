<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/confirm_and_pay_order_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - confirm_and_pay_order_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">13</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import 'order_session_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : class ConfirmAndPayOrderEntity extends BaseEntity {</span>
<span id="L8"><span class="lineNum">       8</span>              :   /// Status code [CommonHttpClient.BAD_REQUEST]</span>
<span id="L9"><span class="lineNum">       9</span>              :   static const String verdictExpiredToken = 'expired_token';</span>
<span id="L10"><span class="lineNum">      10</span>              :   static const String verdictInvalidCredential = 'invalid_credential';</span>
<span id="L11"><span class="lineNum">      11</span>              :   static const String verdictOneLastTry = 'one_last_try';</span>
<span id="L12"><span class="lineNum">      12</span>              :   static const String verdictSessionExpired = 'session_expired';</span>
<span id="L13"><span class="lineNum">      13</span>              :   static const String verdictSessionNotOpened = 'session_not_opened';</span>
<span id="L14"><span class="lineNum">      14</span>              :   static const String verdictUserInactive = 'user_inactive';</span>
<span id="L15"><span class="lineNum">      15</span>              :   static const String verdictPaymentMethodInvalid = 'payment_method_invalid';</span>
<span id="L16"><span class="lineNum">      16</span>              :   static const String verdictCreditLimitInsufficientLimit = 'credit_limit_insufficient';</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              :   /// Status code [CommonHttpClient.LIMIT_EXCEEDED]</span>
<span id="L19"><span class="lineNum">      19</span>              :   static const String verdictLimitExceed = 'limit_exceed';</span>
<span id="L20"><span class="lineNum">      20</span>              :   static const String verdictTransactionTooSoon = 'transaction_too_soon';</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              :   /// Show toast (snack bar) error</span>
<span id="L23"><span class="lineNum">      23</span>              :   static const String verdictMissingPaymentMethod = 'payment_invalid';</span>
<span id="L24"><span class="lineNum">      24</span>              :   static const String verdictPromotionInvalid = 'promotion_invalid';</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   /// PRD: https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3271655937/Payment+-+Response+code+V2#Update-%26-Confirm-Session</span>
<span id="L27"><span class="lineNum">      27</span>              :   /// The server does not return any verdicts below</span>
<span id="L28"><span class="lineNum">      28</span>              :   ///  However, it is mentioned in the PRD (part [Update &amp; Confirm Session]), so it must be handled</span>
<span id="L29"><span class="lineNum">      29</span>              :   static const String verdictPromotionExpired = 'promotion_expired_data';</span>
<span id="L30"><span class="lineNum">      30</span>              :   static const String verdictPromotionUnqualified = 'promotion_unqualified';</span>
<span id="L31"><span class="lineNum">      31</span>              :   static const String verdictPromotionDuplicate = 'promotion_duplicate';</span>
<span id="L32"><span class="lineNum">      32</span>              :   static const String verdictPromotionPermissionDenied = 'promotion_permission_denied';</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span>              :   final OrderSessionEntity? session;</span>
<span id="L35"><span class="lineNum">      35</span>              : </span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :   ConfirmAndPayOrderEntity({</span></span>
<span id="L37"><span class="lineNum">      37</span>              :     this.session,</span>
<span id="L38"><span class="lineNum">      38</span>              :   });</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :   ConfirmAndPayOrderEntity.fromBaseResponse(BaseResponse super.baseResponse)</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :       : session = (baseResponse.data?['session'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :             ? OrderSessionEntity.fromJson(</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :                 baseResponse.data?['session'] as Map&lt;String, dynamic&gt;,</span></span>
<span id="L44"><span class="lineNum">      44</span>              :               )</span>
<span id="L45"><span class="lineNum">      45</span>              :             : null,</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :   ConfirmAndPayOrderEntity.unserializable()</span></span>
<span id="L49"><span class="lineNum">      49</span>              :       : session = null,</span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L53"><span class="lineNum">      53</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :     json.addAll(&lt;String, dynamic&gt;{'session': session?.toJson()});</span></span>
<span id="L56"><span class="lineNum">      56</span>              :     return json;</span>
<span id="L57"><span class="lineNum">      57</span>              :   }</span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L60"><span class="lineNum">      60</span>              :   String toString() {</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :     return 'ConfirmAndPayOrderEntity{session: $session}';</span></span>
<span id="L62"><span class="lineNum">      62</span>              :   }</span>
<span id="L63"><span class="lineNum">      63</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
