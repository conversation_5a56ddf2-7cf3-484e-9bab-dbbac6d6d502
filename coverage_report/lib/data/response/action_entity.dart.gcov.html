<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/action_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - action_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">52</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : /// [ActionEntity] is defined at</span>
<span id="L2"><span class="lineNum">       2</span>              : /// https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3303276740/Controlling+the+Action+of+EVO+Mobile+app#1.-Copy-promotion-code-and-mark-used</span>
<span id="L3"><span class="lineNum">       3</span>              : class ActionEntity {</span>
<span id="L4"><span class="lineNum">       4</span>              :   final ArgsEntity? args;</span>
<span id="L5"><span class="lineNum">       5</span>              :   final String? type;</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span> <span class="tlaUNC">           0 :   ActionEntity({this.args, this.type});</span></span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span> <span class="tlaUNC">           0 :   ActionEntity.fromJson(Map&lt;dynamic, dynamic&gt; json)</span></span>
<span id="L10"><span class="lineNum">      10</span> <span class="tlaUNC">           0 :       : args = (json['args'] as Map&lt;dynamic, dynamic&gt;?) != null</span></span>
<span id="L11"><span class="lineNum">      11</span> <span class="tlaUNC">           0 :             ? ArgsEntity.fromJson(json['args'] as Map&lt;dynamic, dynamic&gt;)</span></span>
<span id="L12"><span class="lineNum">      12</span>              :             : null,</span>
<span id="L13"><span class="lineNum">      13</span> <span class="tlaUNC">           0 :         type = json['type'] as String?;</span></span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; &lt;String, dynamic&gt;{'args': args?.toJson(), 'type': type};</span></span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span> <span class="tlaUNC">           0 :   ActionEntity copyWith({</span></span>
<span id="L18"><span class="lineNum">      18</span>              :     String? type,</span>
<span id="L19"><span class="lineNum">      19</span>              :     ArgsEntity? args,</span>
<span id="L20"><span class="lineNum">      20</span>              :   }) =&gt;</span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaUNC">           0 :       ActionEntity(</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaUNC">           0 :         type: type ?? this.type,</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :         args: args ?? this.args,</span></span>
<span id="L24"><span class="lineNum">      24</span>              :       );</span>
<span id="L25"><span class="lineNum">      25</span>              : }</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span>              : class ArgsEntity {</span>
<span id="L28"><span class="lineNum">      28</span>              :   final String? link;</span>
<span id="L29"><span class="lineNum">      29</span>              :   final String? screenName;</span>
<span id="L30"><span class="lineNum">      30</span>              :   final String? actionLabel;</span>
<span id="L31"><span class="lineNum">      31</span>              :   final ParametersEntity? parameters;</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span>              :   /// [nextAction] is defined at</span>
<span id="L34"><span class="lineNum">      34</span>              :   /// https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3303276740/Controlling+the+Action+of+EVO+Mobile+app#Open-Web-View</span>
<span id="L35"><span class="lineNum">      35</span>              :   final ActionEntity? nextAction;</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :   ArgsEntity({</span></span>
<span id="L38"><span class="lineNum">      38</span>              :     this.link,</span>
<span id="L39"><span class="lineNum">      39</span>              :     this.screenName,</span>
<span id="L40"><span class="lineNum">      40</span>              :     this.actionLabel,</span>
<span id="L41"><span class="lineNum">      41</span>              :     this.parameters,</span>
<span id="L42"><span class="lineNum">      42</span>              :     this.nextAction,</span>
<span id="L43"><span class="lineNum">      43</span>              :   });</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :   ArgsEntity.fromJson(Map&lt;dynamic, dynamic&gt; json)</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :       : link = json['link'] as String?,</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :         screenName = json['screen_name'] as String?,</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :         actionLabel = json['action_label'] as String?,</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :         parameters = (json['parameters'] as Map&lt;dynamic, dynamic&gt;?) != null</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :             ? ParametersEntity.fromJson(json['parameters'] as Map&lt;dynamic, dynamic&gt;)</span></span>
<span id="L51"><span class="lineNum">      51</span>              :             : null,</span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :         nextAction = (json['next_action'] as Map&lt;dynamic, dynamic&gt;?) != null</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :             ? ActionEntity.fromJson(json['next_action'] as Map&lt;dynamic, dynamic&gt;)</span></span>
<span id="L54"><span class="lineNum">      54</span>              :             : null;</span>
<span id="L55"><span class="lineNum">      55</span>              : </span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; &lt;String, dynamic&gt;{</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :         'link': link,</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :         'screen_name': screenName,</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :         'action_label': actionLabel,</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :         'parameters': parameters?.toJson(),</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :         'next_action': nextAction?.toJson(),</span></span>
<span id="L62"><span class="lineNum">      62</span>              :       };</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :   ArgsEntity copyWith({</span></span>
<span id="L65"><span class="lineNum">      65</span>              :     String? link,</span>
<span id="L66"><span class="lineNum">      66</span>              :     String? screenName,</span>
<span id="L67"><span class="lineNum">      67</span>              :     String? actionLabel,</span>
<span id="L68"><span class="lineNum">      68</span>              :     ParametersEntity? parameters,</span>
<span id="L69"><span class="lineNum">      69</span>              :     ActionEntity? nextAction,</span>
<span id="L70"><span class="lineNum">      70</span>              :   }) =&gt;</span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :       ArgsEntity(</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :         link: link ?? this.link,</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :         screenName: screenName ?? this.screenName,</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :         actionLabel: actionLabel ?? this.actionLabel,</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :         parameters: parameters ?? this.parameters,</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :         nextAction: nextAction ?? this.nextAction,</span></span>
<span id="L77"><span class="lineNum">      77</span>              :       );</span>
<span id="L78"><span class="lineNum">      78</span>              : }</span>
<span id="L79"><span class="lineNum">      79</span>              : </span>
<span id="L80"><span class="lineNum">      80</span>              : /// Defined at</span>
<span id="L81"><span class="lineNum">      81</span>              : /// https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3303276740/Controlling+the+Action+of+EVO+Mobile+app#Open-an-App-Screen</span>
<span id="L82"><span class="lineNum">      82</span>              : class ParametersEntity {</span>
<span id="L83"><span class="lineNum">      83</span>              :   /// Defined at https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3303276740/Controlling+the+Action+of+EVO+Mobile+app#Open-an-App-Screen</span>
<span id="L84"><span class="lineNum">      84</span>              :   final String? id;</span>
<span id="L85"><span class="lineNum">      85</span>              : </span>
<span id="L86"><span class="lineNum">      86</span>              :   /// Defined at https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3303276740/Controlling+the+Action+of+EVO+Mobile+app#1.-Copy-promotion-code-and-mark-used</span>
<span id="L87"><span class="lineNum">      87</span>              :   final String? code;</span>
<span id="L88"><span class="lineNum">      88</span>              : </span>
<span id="L89"><span class="lineNum">      89</span>              :   /// Defined at: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3303276740/Controlling+the+Action+of+EVO+Mobile+app#Screen-List-in-the-EVO-App</span>
<span id="L90"><span class="lineNum">      90</span>              :   final int? creditLimit;</span>
<span id="L91"><span class="lineNum">      91</span>              : </span>
<span id="L92"><span class="lineNum">      92</span>              :   /// Defined at: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3303276740/Controlling+the+Action+of+EVO+Mobile+app#Screen-List-in-the-EVO-App</span>
<span id="L93"><span class="lineNum">      93</span>              :   final int? posLimitAllow;</span>
<span id="L94"><span class="lineNum">      94</span>              : </span>
<span id="L95"><span class="lineNum">      95</span>              :   /// Defined at: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3303276740/Controlling+the+Action+of+EVO+Mobile+app#Screen-List-in-the-EVO-App</span>
<span id="L96"><span class="lineNum">      96</span>              :   final bool? shouldOpenCashbackSheet;</span>
<span id="L97"><span class="lineNum">      97</span>              : </span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :   ParametersEntity({</span></span>
<span id="L99"><span class="lineNum">      99</span>              :     this.id,</span>
<span id="L100"><span class="lineNum">     100</span>              :     this.code,</span>
<span id="L101"><span class="lineNum">     101</span>              :     this.creditLimit,</span>
<span id="L102"><span class="lineNum">     102</span>              :     this.posLimitAllow,</span>
<span id="L103"><span class="lineNum">     103</span>              :     this.shouldOpenCashbackSheet,</span>
<span id="L104"><span class="lineNum">     104</span>              :   });</span>
<span id="L105"><span class="lineNum">     105</span>              : </span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :   ParametersEntity.fromJson(Map&lt;dynamic, dynamic&gt; json)</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :       : id = json['id'] as String?,</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :         code = json['code'] as String?,</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :         creditLimit = json['credit_limit'] as int?,</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :         posLimitAllow = json['pos_limit_allow'] as int?,</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :         shouldOpenCashbackSheet = json['should_open_cashback_sheet'] == 'true';</span></span>
<span id="L112"><span class="lineNum">     112</span>              : </span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; &lt;String, dynamic&gt;{</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :         'id': id,</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :         'code': code,</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :         'credit_limit': creditLimit,</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :         'pos_limit_allow': posLimitAllow,</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :         'should_open_cashback_sheet': shouldOpenCashbackSheet.toString(),</span></span>
<span id="L119"><span class="lineNum">     119</span>              :       };</span>
<span id="L120"><span class="lineNum">     120</span>              : </span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :   ParametersEntity copyWith({</span></span>
<span id="L122"><span class="lineNum">     122</span>              :     String? id,</span>
<span id="L123"><span class="lineNum">     123</span>              :     String? code,</span>
<span id="L124"><span class="lineNum">     124</span>              :     int? posLimitAllow,</span>
<span id="L125"><span class="lineNum">     125</span>              :     int? creditLimit,</span>
<span id="L126"><span class="lineNum">     126</span>              :     bool? shouldOpenCashbackSheet,</span>
<span id="L127"><span class="lineNum">     127</span>              :   }) =&gt;</span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :       ParametersEntity(</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :         id: id ?? this.id,</span></span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :         code: code ?? this.code,</span></span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :         creditLimit: creditLimit ?? this.creditLimit,</span></span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :         posLimitAllow: posLimitAllow ?? this.posLimitAllow,</span></span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :         shouldOpenCashbackSheet: shouldOpenCashbackSheet ?? this.shouldOpenCashbackSheet,</span></span>
<span id="L134"><span class="lineNum">     134</span>              :       );</span>
<span id="L135"><span class="lineNum">     135</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
