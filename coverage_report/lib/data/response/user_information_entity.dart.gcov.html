<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/user_information_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - user_information_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">2.2&nbsp;%</td>
            <td class="headerCovTableEntry">45</td>
            <td class="headerCovTableEntry">1</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/common_package/common_package.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../feature/profile/model/gender.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../resources/global.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../resources/ui_strings.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../util/functions.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : </span>
<span id="L9"><span class="lineNum">       9</span>              : class UserInformationEntity extends Equatable {</span>
<span id="L10"><span class="lineNum">      10</span> <span class="tlaGNC">           2 :   const UserInformationEntity({</span></span>
<span id="L11"><span class="lineNum">      11</span>              :     this.fullName,</span>
<span id="L12"><span class="lineNum">      12</span>              :     this.gender,</span>
<span id="L13"><span class="lineNum">      13</span>              :     this.birthday,</span>
<span id="L14"><span class="lineNum">      14</span>              :     this.identityCardIssueDate,</span>
<span id="L15"><span class="lineNum">      15</span>              :     this.identityCardNumber,</span>
<span id="L16"><span class="lineNum">      16</span>              :     this.phoneNumber,</span>
<span id="L17"><span class="lineNum">      17</span>              :     this.email,</span>
<span id="L18"><span class="lineNum">      18</span>              :     this.avatarUrl,</span>
<span id="L19"><span class="lineNum">      19</span>              :   });</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              :   final String? fullName;</span>
<span id="L22"><span class="lineNum">      22</span>              :   final String? gender;</span>
<span id="L23"><span class="lineNum">      23</span>              :   final String? identityCardIssueDate;</span>
<span id="L24"><span class="lineNum">      24</span>              :   final String? identityCardNumber;</span>
<span id="L25"><span class="lineNum">      25</span>              :   final String? phoneNumber;</span>
<span id="L26"><span class="lineNum">      26</span>              :   final String? birthday;</span>
<span id="L27"><span class="lineNum">      27</span>              :   final String? email;</span>
<span id="L28"><span class="lineNum">      28</span>              :   final String? avatarUrl;</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   factory UserInformationEntity.fromJson(Map&lt;String, dynamic&gt; json) =&gt; UserInformationEntity(</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :         fullName: json['full_name'] as String?,</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :         gender: json['gender'] as String?,</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :         birthday: json['birthday'] as String?,</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :         identityCardIssueDate: json['identity_card_issue_date'] as String?,</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :         identityCardNumber: json['identity_card_number'] as String?,</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :         phoneNumber: json['phone_number'] as String?,</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :         email: json['email'] as String?,</span></span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :         avatarUrl: json['avatar_url'] as String?,</span></span>
<span id="L39"><span class="lineNum">      39</span>              :       );</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; &lt;String, dynamic&gt;{</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :         'full_name': fullName,</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :         'gender': gender,</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :         'birthday': birthday,</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :         'identity_card_issue_date': identityCardIssueDate,</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :         'identity_card_number': identityCardNumber,</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :         'phone_number': phoneNumber,</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :         'email': email,</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :         'avatar_url': avatarUrl,</span></span>
<span id="L50"><span class="lineNum">      50</span>              :       };</span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L53"><span class="lineNum">      53</span>              :   String toString() {</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :     return 'UserInformation{fullName: $fullName, gender: $gender, identityCardIssueDate: $identityCardIssueDate, identityCardNumber: $identityCardNumber, phoneNumber: $phoneNumber}';</span></span>
<span id="L55"><span class="lineNum">      55</span>              :   }</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :   List&lt;Object?&gt; get props =&gt; &lt;Object?&gt;[</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :         fullName,</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :         gender,</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :         birthday,</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :         identityCardIssueDate,</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :         identityCardNumber,</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :         phoneNumber,</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :         email,</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :         avatarUrl,</span></span>
<span id="L67"><span class="lineNum">      67</span>              :       ];</span>
<span id="L68"><span class="lineNum">      68</span>              : }</span>
<span id="L69"><span class="lineNum">      69</span>              : </span>
<span id="L70"><span class="lineNum">      70</span>              : extension UserInformationExtension on UserInformationEntity {</span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :   String getGivenName() {</span></span>
<span id="L72"><span class="lineNum">      72</span>              :     String prefix = '';</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :     switch (Gender.formatGenderString(gender)) {</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :       case Gender.male:</span></span>
<span id="L75"><span class="lineNum">      75</span>              :         prefix = EvoStrings.prefixMale;</span>
<span id="L76"><span class="lineNum">      76</span>              :         break;</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :       case Gender.female:</span></span>
<span id="L78"><span class="lineNum">      78</span>              :         prefix = EvoStrings.prefixFemale;</span>
<span id="L79"><span class="lineNum">      79</span>              :         break;</span>
<span id="L80"><span class="lineNum">      80</span>              :       default:</span>
<span id="L81"><span class="lineNum">      81</span>              :         prefix = '';</span>
<span id="L82"><span class="lineNum">      82</span>              :         break;</span>
<span id="L83"><span class="lineNum">      83</span>              :     }</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :     if (fullName != null &amp;&amp; fullName!.isNotEmpty) {</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :       final String name = fullName!.trim().split(' ').last;</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :       return '$prefix $name'.trim().uppercaseFirstLetterEachWord();</span></span>
<span id="L88"><span class="lineNum">      88</span>              :     } else {</span>
<span id="L89"><span class="lineNum">      89</span>              :       return EvoStrings.unknownGivenName;</span>
<span id="L90"><span class="lineNum">      90</span>              :     }</span>
<span id="L91"><span class="lineNum">      91</span>              :   }</span>
<span id="L92"><span class="lineNum">      92</span>              : </span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :   bool hasFullName() =&gt; fullName?.isNotEmpty == true;</span></span>
<span id="L94"><span class="lineNum">      94</span>              : </span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :   String? getDisplayName() {</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :     if (hasFullName()) {</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :       return fullName;</span></span>
<span id="L98"><span class="lineNum">      98</span>              :     }</span>
<span id="L99"><span class="lineNum">      99</span>              : </span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :     return phoneNumber?.hiddenByFormat((phoneNumber?.length ?? 0) - phoneNumberFormatNumOfLastShow);</span></span>
<span id="L101"><span class="lineNum">     101</span>              :   }</span>
<span id="L102"><span class="lineNum">     102</span>              : </span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :   String? getFormattedBirthday() {</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :     return evoUtilFunction.formatBirthday(birthday);</span></span>
<span id="L105"><span class="lineNum">     105</span>              :   }</span>
<span id="L106"><span class="lineNum">     106</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
