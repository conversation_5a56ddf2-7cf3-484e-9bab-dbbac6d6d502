<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/voucher_list_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - voucher_list_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">16</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import 'voucher_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : class VoucherListEntity extends BaseEntity {</span>
<span id="L8"><span class="lineNum">       8</span>              :   final String? title;</span>
<span id="L9"><span class="lineNum">       9</span>              :   final String? description;</span>
<span id="L10"><span class="lineNum">      10</span>              :   final List&lt;VoucherEntity&gt;? vouchers;</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaUNC">           0 :   VoucherListEntity({</span></span>
<span id="L13"><span class="lineNum">      13</span>              :     this.title,</span>
<span id="L14"><span class="lineNum">      14</span>              :     this.description,</span>
<span id="L15"><span class="lineNum">      15</span>              :     this.vouchers,</span>
<span id="L16"><span class="lineNum">      16</span>              :   });</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaUNC">           0 :   VoucherListEntity.unserializable()</span></span>
<span id="L19"><span class="lineNum">      19</span>              :       : title = null,</span>
<span id="L20"><span class="lineNum">      20</span>              :         description = null,</span>
<span id="L21"><span class="lineNum">      21</span>              :         vouchers = null,</span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :   VoucherListEntity.fromBaseResponse(BaseResponse super.baseResponse)</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaUNC">           0 :       : title = baseResponse.data?['title'] as String?,</span></span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaUNC">           0 :         description = baseResponse.data?['description'] as String?,</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :         vouchers = (baseResponse.data?['vouchers'] as List&lt;dynamic&gt;?)</span></span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :             ?.map((dynamic e) =&gt; VoucherEntity.fromJson(e as Map&lt;String, dynamic&gt;))</span></span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaUNC">           0 :             .toList(),</span></span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L33"><span class="lineNum">      33</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :     json.addAll(&lt;String, dynamic&gt;{</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :       'title': title,</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :       'description': description,</span></span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :       'vouchers': vouchers?.map((VoucherEntity e) =&gt; e.toJson()).toList()</span></span>
<span id="L39"><span class="lineNum">      39</span>              :     });</span>
<span id="L40"><span class="lineNum">      40</span>              :     return json;</span>
<span id="L41"><span class="lineNum">      41</span>              :   }</span>
<span id="L42"><span class="lineNum">      42</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
