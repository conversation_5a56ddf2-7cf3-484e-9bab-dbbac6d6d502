<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/linked_card_status_checking_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - linked_card_status_checking_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">16</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import 'action_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : enum LinkCardStatusChallengeType {</span>
<span id="L8"><span class="lineNum">       8</span>              :   faceOtp('face_otp'),</span>
<span id="L9"><span class="lineNum">       9</span>              :   faceAuth('face_auth'),</span>
<span id="L10"><span class="lineNum">      10</span>              :   none('none');</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              :   final String value;</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span>              :   const LinkCardStatusChallengeType(this.value);</span>
<span id="L15"><span class="lineNum">      15</span>              : }</span>
<span id="L16"><span class="lineNum">      16</span>              : </span>
<span id="L17"><span class="lineNum">      17</span>              : class LinkedCardStatusCheckingEntity extends BaseEntity {</span>
<span id="L18"><span class="lineNum">      18</span>              :   static const String verdictSuccess = 'success';</span>
<span id="L19"><span class="lineNum">      19</span>              :   static const String verdictUnfulfilledCard = 'unfulfilled_card';</span>
<span id="L20"><span class="lineNum">      20</span>              :   static const String verdictWaitingForCardIssuing = 'waiting_for_card_issuing';</span>
<span id="L21"><span class="lineNum">      21</span>              :   static const String verdictUnqualifiedCard = 'unqualified_card';</span>
<span id="L22"><span class="lineNum">      22</span>              :   static const String verdictDuplicatedLinkRequest = 'duplicated_link_request';</span>
<span id="L23"><span class="lineNum">      23</span>              :   static const String verdictUnqualifiedUserInformation = 'unqualified_user_information';</span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span>              :   // PREPARE LINK CARD</span>
<span id="L26"><span class="lineNum">      26</span>              :   static const String verdictLinkCardInvalidPhoneNumber = 'link_card_invalid_phone_number';</span>
<span id="L27"><span class="lineNum">      27</span>              :   static const String verdictLinkCardAlreadyLinked = 'link_card_already_linked';</span>
<span id="L28"><span class="lineNum">      28</span>              :   static const String verdictLinkCardUnqualifiedUserInformation = 'unqualified_user_information';</span>
<span id="L29"><span class="lineNum">      29</span>              :   static const String verdictLinkCardInvalidParameters = 'link_card_invalid_parameters';</span>
<span id="L30"><span class="lineNum">      30</span>              :   static const String verdictLinkCardLinkRequestIsProcessing =</span>
<span id="L31"><span class="lineNum">      31</span>              :       'link_card_link_request_is_processing';</span>
<span id="L32"><span class="lineNum">      32</span>              :   static const String verdictLinkCardInvalidBankCode = 'link_card_invalid_bank_code';</span>
<span id="L33"><span class="lineNum">      33</span>              :   static const String verdictLinkCardNotFoundLinkInfo = 'link_card_not_found_link_info';</span>
<span id="L34"><span class="lineNum">      34</span>              :   static const String verdictLinkCardBankProductNotSupported =</span>
<span id="L35"><span class="lineNum">      35</span>              :       'link_card_bank_product_not_supported';</span>
<span id="L36"><span class="lineNum">      36</span>              :   static const String verdictLinkCardFailure = 'link_card_failure';</span>
<span id="L37"><span class="lineNum">      37</span>              :   static const String verdictFailureAll = 'failure';</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span>              :   final String? challengeType;</span>
<span id="L40"><span class="lineNum">      40</span>              :   final String? sessionToken;</span>
<span id="L41"><span class="lineNum">      41</span>              :   final ActionEntity? action;</span>
<span id="L42"><span class="lineNum">      42</span>              :   final String? linkCardRequestId;</span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :   LinkedCardStatusCheckingEntity({</span></span>
<span id="L45"><span class="lineNum">      45</span>              :     this.challengeType,</span>
<span id="L46"><span class="lineNum">      46</span>              :     this.sessionToken,</span>
<span id="L47"><span class="lineNum">      47</span>              :     this.action,</span>
<span id="L48"><span class="lineNum">      48</span>              :     this.linkCardRequestId,</span>
<span id="L49"><span class="lineNum">      49</span>              :   });</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :   LinkedCardStatusCheckingEntity.unserializable()</span></span>
<span id="L52"><span class="lineNum">      52</span>              :       : challengeType = null,</span>
<span id="L53"><span class="lineNum">      53</span>              :         sessionToken = null,</span>
<span id="L54"><span class="lineNum">      54</span>              :         action = null,</span>
<span id="L55"><span class="lineNum">      55</span>              :         linkCardRequestId = null,</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :   LinkedCardStatusCheckingEntity.fromBaseResponse(BaseResponse super.response)</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :       : challengeType = response.data?['challenge_type'] as String?,</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :         sessionToken = response.data?['session_token'] as String?,</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :         action = (response.data?['action'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :             ? ActionEntity.fromJson(response.data?['action'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L63"><span class="lineNum">      63</span>              :             : null,</span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :         linkCardRequestId = response.data?['link_card_request_id'] as String?,</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L66"><span class="lineNum">      66</span>              : </span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L68"><span class="lineNum">      68</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :     json['challenge_type'] = challengeType;</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :     json['session_token'] = sessionToken;</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :     json['link_card_request_id'] = linkCardRequestId;</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :     json['action'] = action?.toJson();</span></span>
<span id="L74"><span class="lineNum">      74</span>              :     return json;</span>
<span id="L75"><span class="lineNum">      75</span>              :   }</span>
<span id="L76"><span class="lineNum">      76</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
