<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/dop_native/dop_native_bootstrap_auth_settings_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response/dop_native">lib/data/response/dop_native</a> - dop_native_bootstrap_auth_settings_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">15</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:collection/collection.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : enum DOPNativeAuthType {</span>
<span id="L7"><span class="lineNum">       7</span>              :   /// Sms OTP: user is registering a new DOP flow, redirect user to sms otp screen</span>
<span id="L8"><span class="lineNum">       8</span>              :   /// Refer to API Spec: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3529310243/S2+KH+nh+p+m+x+c+th+c+OTP</span>
<span id="L9"><span class="lineNum">       9</span>              :   smsOTP('otp'),</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              :   /// Face OTP: user had already registered a DOP flow, and completed eKYC_selfie step by then; redirect user to back flow (face otp screen)</span>
<span id="L12"><span class="lineNum">      12</span>              :   /// Refer to PRD: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3508175096/S21.+Drop-off+flow</span>
<span id="L13"><span class="lineNum">      13</span>              :   faceOTP('face_id'),</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span>              :   /// E-sign OTP: User had already goto e-sign otp and drop-off, using when user drop-off, input phone again, will jump to e-sign otp if receive this type</span>
<span id="L16"><span class="lineNum">      16</span>              :   /// Refer to PRD: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3529310243/S2+KH+nh+p+m+x+c+th+c+OTP#Esign-OTP-(auth_type-%3D-%E2%80%9Cesign.otp%E2%80%9C)</span>
<span id="L17"><span class="lineNum">      17</span>              :   eSignOTP('esign.otp'),</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              :   /// ID Card Auth:</span>
<span id="L20"><span class="lineNum">      20</span>              :   ///   - if it is under x-mins, it will grant access_token to the mobile (only have permission to call APIs in state ekyc.id_card and getApplicationState)</span>
<span id="L21"><span class="lineNum">      21</span>              :   ///   - after user complete the ekyc.id_card, DOP BE will return a full permission access token → mobile will replace the old access_token by it</span>
<span id="L22"><span class="lineNum">      22</span>              :   /// Refer to: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3922886736</span>
<span id="L23"><span class="lineNum">      23</span>              :   idCard('id_card_auth');</span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span>              :   final String value;</span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span>              :   const DOPNativeAuthType(this.value);</span>
<span id="L28"><span class="lineNum">      28</span>              : }</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span>              : class DOPNativeBootstrapAuthSettingsEntity extends BaseEntity {</span>
<span id="L31"><span class="lineNum">      31</span>              :   final String? authType;</span>
<span id="L32"><span class="lineNum">      32</span>              :   final String? accessToken;</span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :   DOPNativeBootstrapAuthSettingsEntity({</span></span>
<span id="L35"><span class="lineNum">      35</span>              :     this.authType,</span>
<span id="L36"><span class="lineNum">      36</span>              :     this.accessToken,</span>
<span id="L37"><span class="lineNum">      37</span>              :   });</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :   DOPNativeBootstrapAuthSettingsEntity.unserializable()</span></span>
<span id="L40"><span class="lineNum">      40</span>              :       : authType = null,</span>
<span id="L41"><span class="lineNum">      41</span>              :         accessToken = null,</span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :   DOPNativeBootstrapAuthSettingsEntity.fromBaseResponse(BaseResponse super.response)</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :       : authType = response.data?['auth_type'],</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :         accessToken = response.data?['access_token'],</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L50"><span class="lineNum">      50</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :     json.addAll(&lt;String, dynamic&gt;{</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :       'auth_type': authType,</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :       'access_token': accessToken,</span></span>
<span id="L55"><span class="lineNum">      55</span>              :     });</span>
<span id="L56"><span class="lineNum">      56</span>              :     return json;</span>
<span id="L57"><span class="lineNum">      57</span>              :   }</span>
<span id="L58"><span class="lineNum">      58</span>              : }</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span>              : extension DOPNativeAuthTypeEx on String {</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :   DOPNativeAuthType? toDOPNativeAuthType() {</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :     return DOPNativeAuthType.values.firstWhereOrNull(</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :       (DOPNativeAuthType type) =&gt; type.value == this,</span></span>
<span id="L64"><span class="lineNum">      64</span>              :     );</span>
<span id="L65"><span class="lineNum">      65</span>              :   }</span>
<span id="L66"><span class="lineNum">      66</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
