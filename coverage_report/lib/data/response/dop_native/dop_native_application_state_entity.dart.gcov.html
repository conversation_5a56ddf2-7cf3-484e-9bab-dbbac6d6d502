<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/dop_native/dop_native_application_state_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response/dop_native">lib/data/response/dop_native</a> - dop_native_application_state_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">85</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : class DOPNativeApplicationStateEntity extends BaseEntity {</span>
<span id="L6"><span class="lineNum">       6</span>              :   final String? currentStep;</span>
<span id="L7"><span class="lineNum">       7</span>              :   final int? flowSelectedAt;</span>
<span id="L8"><span class="lineNum">       8</span>              :   final bool? fullyCompleted;</span>
<span id="L9"><span class="lineNum">       9</span>              :   final bool? isDropOff;</span>
<span id="L10"><span class="lineNum">      10</span>              :   final bool? hasDroppedOff;</span>
<span id="L11"><span class="lineNum">      11</span>              :   final String? subFlowType;</span>
<span id="L12"><span class="lineNum">      12</span>              :   final String? rejectedCode;</span>
<span id="L13"><span class="lineNum">      13</span>              :   final String? rejectedTitle;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final String? lenderLeadID;</span>
<span id="L15"><span class="lineNum">      15</span>              :   final String? screenType;</span>
<span id="L16"><span class="lineNum">      16</span>              :   final List&lt;String&gt;? uiNavigation;</span>
<span id="L17"><span class="lineNum">      17</span>              :   final String? uiVersion;</span>
<span id="L18"><span class="lineNum">      18</span>              :   final FlowConfig? flowConfig;</span>
<span id="L19"><span class="lineNum">      19</span>              :   final String? currentStatus;</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaUNC">           0 :   DOPNativeApplicationStateEntity({</span></span>
<span id="L22"><span class="lineNum">      22</span>              :     this.currentStep,</span>
<span id="L23"><span class="lineNum">      23</span>              :     this.flowSelectedAt,</span>
<span id="L24"><span class="lineNum">      24</span>              :     this.fullyCompleted,</span>
<span id="L25"><span class="lineNum">      25</span>              :     this.isDropOff,</span>
<span id="L26"><span class="lineNum">      26</span>              :     this.hasDroppedOff,</span>
<span id="L27"><span class="lineNum">      27</span>              :     this.subFlowType,</span>
<span id="L28"><span class="lineNum">      28</span>              :     this.rejectedCode,</span>
<span id="L29"><span class="lineNum">      29</span>              :     this.rejectedTitle,</span>
<span id="L30"><span class="lineNum">      30</span>              :     this.lenderLeadID,</span>
<span id="L31"><span class="lineNum">      31</span>              :     this.screenType,</span>
<span id="L32"><span class="lineNum">      32</span>              :     this.uiNavigation,</span>
<span id="L33"><span class="lineNum">      33</span>              :     this.uiVersion,</span>
<span id="L34"><span class="lineNum">      34</span>              :     this.flowConfig,</span>
<span id="L35"><span class="lineNum">      35</span>              :     this.currentStatus,</span>
<span id="L36"><span class="lineNum">      36</span>              :   });</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :   DOPNativeApplicationStateEntity.unserializable()</span></span>
<span id="L39"><span class="lineNum">      39</span>              :       : currentStep = null,</span>
<span id="L40"><span class="lineNum">      40</span>              :         flowSelectedAt = null,</span>
<span id="L41"><span class="lineNum">      41</span>              :         fullyCompleted = null,</span>
<span id="L42"><span class="lineNum">      42</span>              :         isDropOff = null,</span>
<span id="L43"><span class="lineNum">      43</span>              :         hasDroppedOff = null,</span>
<span id="L44"><span class="lineNum">      44</span>              :         subFlowType = null,</span>
<span id="L45"><span class="lineNum">      45</span>              :         rejectedCode = null,</span>
<span id="L46"><span class="lineNum">      46</span>              :         rejectedTitle = null,</span>
<span id="L47"><span class="lineNum">      47</span>              :         lenderLeadID = null,</span>
<span id="L48"><span class="lineNum">      48</span>              :         screenType = null,</span>
<span id="L49"><span class="lineNum">      49</span>              :         uiNavigation = null,</span>
<span id="L50"><span class="lineNum">      50</span>              :         uiVersion = null,</span>
<span id="L51"><span class="lineNum">      51</span>              :         flowConfig = null,</span>
<span id="L52"><span class="lineNum">      52</span>              :         currentStatus = null,</span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :   DOPNativeApplicationStateEntity.fromBaseResponse(BaseResponse super.response)</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :       : currentStep = response.data?['current_step'],</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :         flowSelectedAt = response.data?['flow_selected_at'],</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :         fullyCompleted = response.data?['fully_completed'],</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :         isDropOff = response.data?['is_drop_off'],</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :         hasDroppedOff = response.data?['has_dropped_off'],</span></span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :         subFlowType = response.data?['sub_flow_type'],</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :         rejectedCode = response.data?['rejected_code'],</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :         rejectedTitle = response.data?['rejected_title'],</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :         lenderLeadID = response.data?['lender_lead_id'],</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :         screenType = response.data?['screen_type'],</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :         uiNavigation = (response.data?['ui_navigation'] as List&lt;dynamic&gt;?)</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :             ?.map((dynamic e) =&gt; e as String)</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :             .toList(),</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :         uiVersion = response.data?['ui_version'],</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :         flowConfig = response.data?['flow_config'] != null</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :             ? FlowConfig.fromJson(response.data?['flow_config'])</span></span>
<span id="L72"><span class="lineNum">      72</span>              :             : null,</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :         currentStatus = response.data?['current_status'],</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :   DOPNativeApplicationStateEntity.fromJson(Map&lt;String, dynamic&gt; json)</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :       : currentStep = json['current_step'],</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :         flowSelectedAt = json['flow_selected_at'],</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :         fullyCompleted = json['fully_completed'],</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :         isDropOff = json['is_drop_off'],</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :         hasDroppedOff = json['has_dropped_off'],</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :         subFlowType = json['sub_flow_type'],</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :         rejectedCode = json['rejected_code'],</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :         rejectedTitle = json['rejected_title'],</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :         lenderLeadID = json['lender_lead_id'],</span></span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :         screenType = json['screen_type'],</span></span>
<span id="L87"><span class="lineNum">      87</span>              :         uiNavigation =</span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :             (json['ui_navigation'] as List&lt;dynamic&gt;?)?.map((dynamic e) =&gt; e as String).toList(),</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :         uiVersion = json['ui_version'],</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :         flowConfig = json['flow_config'] != null ? FlowConfig.fromJson(json['flow_config']) : null,</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :         currentStatus = json['current_status'];</span></span>
<span id="L92"><span class="lineNum">      92</span>              : </span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L94"><span class="lineNum">      94</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :     return &lt;String, dynamic&gt;{</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :       'current_step': currentStep,</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :       'flow_selected_at': flowSelectedAt,</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :       'fully_completed': fullyCompleted,</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :       'is_drop_off': isDropOff,</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :       'has_dropped_off': hasDroppedOff,</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :       'sub_flow_type': subFlowType,</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :       'rejected_code': rejectedCode,</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :       'rejected_title': rejectedTitle,</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :       'lender_lead_id': lenderLeadID,</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :       'screen_type': screenType,</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :       'ui_navigation': uiNavigation,</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :       'ui_version': uiVersion,</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :       'flow_config': flowConfig?.toJson(),</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :       'current_status': currentStatus,</span></span>
<span id="L110"><span class="lineNum">     110</span>              :     };</span>
<span id="L111"><span class="lineNum">     111</span>              :   }</span>
<span id="L112"><span class="lineNum">     112</span>              : </span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :   DOPNativeApplicationStateEntity copyWith(DOPNativeApplicationStateEntity? entity) {</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :     return DOPNativeApplicationStateEntity(</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :       currentStep: entity?.currentStep ?? currentStep,</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :       flowSelectedAt: entity?.flowSelectedAt ?? flowSelectedAt,</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :       fullyCompleted: entity?.fullyCompleted ?? fullyCompleted,</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :       isDropOff: entity?.isDropOff ?? isDropOff,</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :       hasDroppedOff: entity?.hasDroppedOff ?? hasDroppedOff,</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :       subFlowType: entity?.subFlowType ?? subFlowType,</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :       rejectedCode: entity?.rejectedCode ?? rejectedCode,</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :       rejectedTitle: entity?.rejectedTitle ?? rejectedTitle,</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :       lenderLeadID: entity?.lenderLeadID ?? lenderLeadID,</span></span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :       screenType: entity?.screenType ?? screenType,</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :       uiNavigation: entity?.uiNavigation ?? uiNavigation,</span></span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :       uiVersion: entity?.uiVersion ?? uiVersion,</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :       flowConfig: entity?.flowConfig ?? flowConfig,</span></span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :       currentStatus: entity?.currentStatus ?? currentStatus,</span></span>
<span id="L129"><span class="lineNum">     129</span>              :     );</span>
<span id="L130"><span class="lineNum">     130</span>              :   }</span>
<span id="L131"><span class="lineNum">     131</span>              : }</span>
<span id="L132"><span class="lineNum">     132</span>              : </span>
<span id="L133"><span class="lineNum">     133</span>              : class FlowConfig {</span>
<span id="L134"><span class="lineNum">     134</span>              :   final int? flowSelectedAt;</span>
<span id="L135"><span class="lineNum">     135</span>              :   final String? leadSource;</span>
<span id="L136"><span class="lineNum">     136</span>              :   final String? lenderCode;</span>
<span id="L137"><span class="lineNum">     137</span>              :   final String? maskedPhoneNumber;</span>
<span id="L138"><span class="lineNum">     138</span>              :   final List&lt;String&gt;? requiredSteps;</span>
<span id="L139"><span class="lineNum">     139</span>              :   final String? uiVersion;</span>
<span id="L140"><span class="lineNum">     140</span>              : </span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaUNC">           0 :   FlowConfig({</span></span>
<span id="L142"><span class="lineNum">     142</span>              :     this.flowSelectedAt,</span>
<span id="L143"><span class="lineNum">     143</span>              :     this.leadSource,</span>
<span id="L144"><span class="lineNum">     144</span>              :     this.lenderCode,</span>
<span id="L145"><span class="lineNum">     145</span>              :     this.maskedPhoneNumber,</span>
<span id="L146"><span class="lineNum">     146</span>              :     this.requiredSteps,</span>
<span id="L147"><span class="lineNum">     147</span>              :     this.uiVersion,</span>
<span id="L148"><span class="lineNum">     148</span>              :   });</span>
<span id="L149"><span class="lineNum">     149</span>              : </span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :   FlowConfig.fromJson(Map&lt;dynamic, dynamic&gt;? json)</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :       : flowSelectedAt = json?['flow_selected_at'],</span></span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :         leadSource = json?['lead_source'],</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :         lenderCode = json?['lender_code'],</span></span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaUNC">           0 :         maskedPhoneNumber = json?['masked_phone_number'],</span></span>
<span id="L155"><span class="lineNum">     155</span>              :         requiredSteps =</span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :             (json?['required_steps'] as List&lt;dynamic&gt;?)?.map((dynamic e) =&gt; e as String).toList(),</span></span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :         uiVersion = json?['ui_version'];</span></span>
<span id="L158"><span class="lineNum">     158</span>              : </span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() {</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :     return &lt;String, dynamic&gt;{</span></span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :       'flow_selected_at': flowSelectedAt,</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaUNC">           0 :       'lead_source': leadSource,</span></span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :       'lender_code': lenderCode,</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :       'masked_phone_number': maskedPhoneNumber,</span></span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaUNC">           0 :       'required_steps': requiredSteps,</span></span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :       'ui_version': uiVersion,</span></span>
<span id="L167"><span class="lineNum">     167</span>              :     };</span>
<span id="L168"><span class="lineNum">     168</span>              :   }</span>
<span id="L169"><span class="lineNum">     169</span>              : }</span>
<span id="L170"><span class="lineNum">     170</span>              : </span>
<span id="L171"><span class="lineNum">     171</span>              : /// Ref: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3689086993/CO-10099+Implementation</span>
<span id="L172"><span class="lineNum">     172</span>              : class LeadSource {</span>
<span id="L173"><span class="lineNum">     173</span>              :   static const String mwg = 'mwg';</span>
<span id="L174"><span class="lineNum">     174</span>              :   static const String mwgPOS = 'mwg_pos';</span>
<span id="L175"><span class="lineNum">     175</span>              :   static const String viettelStore = 'viettel_store';</span>
<span id="L176"><span class="lineNum">     176</span>              :   static const String frt = 'frt';</span>
<span id="L177"><span class="lineNum">     177</span>              : </span>
<span id="L178"><span class="lineNum">     178</span>              :   /// default lead_source if entry-point lead_source is null</span>
<span id="L179"><span class="lineNum">     179</span>              :   static const String evoNative = 'evo_native';</span>
<span id="L180"><span class="lineNum">     180</span>              : }</span>
<span id="L181"><span class="lineNum">     181</span>              : </span>
<span id="L182"><span class="lineNum">     182</span>              : /// https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3869671619/CO-11264+DOE+Revamp+Auto_PCB+Flow+for+MWG+Offline#%5BEVO-App%5D</span>
<span id="L183"><span class="lineNum">     183</span>              : enum DOPNativeApplicationStatus {</span>
<span id="L184"><span class="lineNum">     184</span>              :   pending('pending'),</span>
<span id="L185"><span class="lineNum">     185</span>              :   pushing('pushing'),</span>
<span id="L186"><span class="lineNum">     186</span>              :   holding('holding'),</span>
<span id="L187"><span class="lineNum">     187</span>              :   exceedFailure('exceed_failure'),</span>
<span id="L188"><span class="lineNum">     188</span>              :   pushed('pushed'),</span>
<span id="L189"><span class="lineNum">     189</span>              :   skipped('skipped');</span>
<span id="L190"><span class="lineNum">     190</span>              : </span>
<span id="L191"><span class="lineNum">     191</span>              :   final String value;</span>
<span id="L192"><span class="lineNum">     192</span>              : </span>
<span id="L193"><span class="lineNum">     193</span>              :   const DOPNativeApplicationStatus(this.value);</span>
<span id="L194"><span class="lineNum">     194</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
