<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/dop_native/dop_native_register_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response/dop_native">lib/data/response/dop_native</a> - dop_native_register_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">30</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : class DOPNativeRegisterEntity extends BaseEntity {</span>
<span id="L6"><span class="lineNum">       6</span>              :   static const String verdictSuccess = 'success';</span>
<span id="L7"><span class="lineNum">       7</span>              :   static const String verdictUnqualified = 'unqualified';</span>
<span id="L8"><span class="lineNum">       8</span>              :   static const String verdictDuplicate = 'duplicate';</span>
<span id="L9"><span class="lineNum">       9</span>              :   static const String verdictLimitExceeded = 'limit_exceeded';</span>
<span id="L10"><span class="lineNum">      10</span>              :   static const String verdictExistedRecord = 'existed_record';</span>
<span id="L11"><span class="lineNum">      11</span>              :   static const String verdictExpiredPToken = 'expired_ptoken';</span>
<span id="L12"><span class="lineNum">      12</span>              :   static const String verdictInvalidPToken = 'invalid_ptoken';</span>
<span id="L13"><span class="lineNum">      13</span>              :   static const String verdictDuplicateReject = 'duplicate_reject';</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span>              :   final String? token;</span>
<span id="L16"><span class="lineNum">      16</span>              :   final ExistingApp? existingApp;</span>
<span id="L17"><span class="lineNum">      17</span>              :   final String? merchantName;</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaUNC">           0 :   DOPNativeRegisterEntity.unserializable()</span></span>
<span id="L20"><span class="lineNum">      20</span>              :       : token = null,</span>
<span id="L21"><span class="lineNum">      21</span>              :         existingApp = null,</span>
<span id="L22"><span class="lineNum">      22</span>              :         merchantName = null,</span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L24"><span class="lineNum">      24</span>              : </span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaUNC">           0 :   DOPNativeRegisterEntity({</span></span>
<span id="L26"><span class="lineNum">      26</span>              :     this.token,</span>
<span id="L27"><span class="lineNum">      27</span>              :     this.existingApp,</span>
<span id="L28"><span class="lineNum">      28</span>              :     this.merchantName,</span>
<span id="L29"><span class="lineNum">      29</span>              :   });</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :   DOPNativeRegisterEntity.fromBaseResponse(BaseResponse super.baseResponse)</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :       : token = baseResponse.data?['token'] as String?,</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :         existingApp = baseResponse.data?['existing_app'] != null</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :             ? ExistingApp.fromJson(baseResponse.data?['existing_app'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L35"><span class="lineNum">      35</span>              :             : null,</span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :         merchantName = baseResponse.data?['mc_name'] as String?,</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L40"><span class="lineNum">      40</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :     json.addAll(&lt;String, dynamic&gt;{</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :       'token': token,</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :       'existing_app': existingApp?.toJson(),</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :       'mc_name': merchantName,</span></span>
<span id="L46"><span class="lineNum">      46</span>              :     });</span>
<span id="L47"><span class="lineNum">      47</span>              :     return json;</span>
<span id="L48"><span class="lineNum">      48</span>              :   }</span>
<span id="L49"><span class="lineNum">      49</span>              : }</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span>              : class ExistingApp {</span>
<span id="L52"><span class="lineNum">      52</span>              :   final String? uniqueToken;</span>
<span id="L53"><span class="lineNum">      53</span>              :   final bool? isPriority;</span>
<span id="L54"><span class="lineNum">      54</span>              :   final ExistingAppPlatform? platform;</span>
<span id="L55"><span class="lineNum">      55</span>              :   final String? leadSource;</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :   ExistingApp({</span></span>
<span id="L58"><span class="lineNum">      58</span>              :     this.uniqueToken,</span>
<span id="L59"><span class="lineNum">      59</span>              :     this.isPriority,</span>
<span id="L60"><span class="lineNum">      60</span>              :     this.platform,</span>
<span id="L61"><span class="lineNum">      61</span>              :     this.leadSource,</span>
<span id="L62"><span class="lineNum">      62</span>              :   });</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :   factory ExistingApp.fromJson(Map&lt;String, dynamic&gt; json) {</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :     return ExistingApp(</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :       uniqueToken: json['unique_token'] as String?,</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :       isPriority: json['is_priority'] as bool?,</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :       platform: json['platform'] != null ? ExistingAppPlatform.fromValue(json['platform']) : null,</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :       leadSource: json['lead_source'] as String?,</span></span>
<span id="L70"><span class="lineNum">      70</span>              :     );</span>
<span id="L71"><span class="lineNum">      71</span>              :   }</span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() {</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :     return &lt;String, dynamic&gt;{</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :       'unique_token': uniqueToken,</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :       'is_priority': isPriority,</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :       'platform': platform?.value,</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :       'lead_source': leadSource,</span></span>
<span id="L79"><span class="lineNum">      79</span>              :     };</span>
<span id="L80"><span class="lineNum">      80</span>              :   }</span>
<span id="L81"><span class="lineNum">      81</span>              : }</span>
<span id="L82"><span class="lineNum">      82</span>              : </span>
<span id="L83"><span class="lineNum">      83</span>              : enum ExistingAppPlatform {</span>
<span id="L84"><span class="lineNum">      84</span>              :   web('web'),</span>
<span id="L85"><span class="lineNum">      85</span>              :   native('native');</span>
<span id="L86"><span class="lineNum">      86</span>              : </span>
<span id="L87"><span class="lineNum">      87</span>              :   const ExistingAppPlatform(this.value);</span>
<span id="L88"><span class="lineNum">      88</span>              : </span>
<span id="L89"><span class="lineNum">      89</span>              :   final String value;</span>
<span id="L90"><span class="lineNum">      90</span>              : </span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :   static ExistingAppPlatform fromValue(String? value) {</span></span>
<span id="L92"><span class="lineNum">      92</span>              :     switch (value) {</span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :       case 'web':</span></span>
<span id="L94"><span class="lineNum">      94</span>              :         return ExistingAppPlatform.web;</span>
<span id="L95"><span class="lineNum">      95</span>              :       default:</span>
<span id="L96"><span class="lineNum">      96</span>              :         return ExistingAppPlatform.native;</span>
<span id="L97"><span class="lineNum">      97</span>              :     }</span>
<span id="L98"><span class="lineNum">      98</span>              :   }</span>
<span id="L99"><span class="lineNum">      99</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
