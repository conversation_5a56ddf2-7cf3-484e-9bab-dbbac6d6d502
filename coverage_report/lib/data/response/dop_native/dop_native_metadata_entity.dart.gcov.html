<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/dop_native/dop_native_metadata_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response/dop_native">lib/data/response/dop_native</a> - dop_native_metadata_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">20</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:collection/collection.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import 'dop_native_metadata_item_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : class DOPNativeMetadataEntity {</span>
<span id="L8"><span class="lineNum">       8</span>              :   static const String verdictMissingParameters = 'missing_parameters';</span>
<span id="L9"><span class="lineNum">       9</span>              :   static const String verdictForbiddenParameters = 'forbidden_parameters';</span>
<span id="L10"><span class="lineNum">      10</span>              :   static const String verdictInvalidParameters = 'invalid_parameters';</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              :   final List&lt;DOPNativeMetadataItemEntity&gt;? metadata;</span>
<span id="L13"><span class="lineNum">      13</span>              :   final int? statusCode;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final String? message;</span>
<span id="L15"><span class="lineNum">      15</span>              :   final String? time;</span>
<span id="L16"><span class="lineNum">      16</span>              :   final String? verdict;</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaUNC">           0 :   DOPNativeMetadataEntity({</span></span>
<span id="L19"><span class="lineNum">      19</span>              :     this.metadata,</span>
<span id="L20"><span class="lineNum">      20</span>              :     this.statusCode,</span>
<span id="L21"><span class="lineNum">      21</span>              :     this.message,</span>
<span id="L22"><span class="lineNum">      22</span>              :     this.time,</span>
<span id="L23"><span class="lineNum">      23</span>              :     this.verdict,</span>
<span id="L24"><span class="lineNum">      24</span>              :   });</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaUNC">           0 :   DOPNativeMetadataEntity.unserializable()</span></span>
<span id="L27"><span class="lineNum">      27</span>              :       : metadata = null,</span>
<span id="L28"><span class="lineNum">      28</span>              :         statusCode = CommonHttpClient.INVALID_FORMAT,</span>
<span id="L29"><span class="lineNum">      29</span>              :         message = null,</span>
<span id="L30"><span class="lineNum">      30</span>              :         time = null,</span>
<span id="L31"><span class="lineNum">      31</span>              :         verdict = null;</span>
<span id="L32"><span class="lineNum">      32</span>              : </span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :   DOPNativeMetadataEntity.fromJson(BaseResponse response)</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :       : metadata = (response.response?['data'] as List&lt;dynamic&gt;?)</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :             ?.map((dynamic e) =&gt; DOPNativeMetadataItemEntity.fromJson(e as Map&lt;String, dynamic&gt;))</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :             .toList(),</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :         statusCode = response.statusCode,</span></span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :         message = response.response?['message'] as String?,</span></span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :         time = response.response?['time'] as String?,</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :         verdict = response.response?['verdict'] as String?;</span></span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() {</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = &lt;String, dynamic&gt;{};</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :     json.addAll(&lt;String, dynamic&gt;{</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :       'data': metadata?.map((DOPNativeMetadataItemEntity v) =&gt; v.toJson()).toList(),</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :       'statusCode': statusCode,</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :       'message': message,</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :       'time': time,</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :       'verdict': verdict,</span></span>
<span id="L50"><span class="lineNum">      50</span>              :     });</span>
<span id="L51"><span class="lineNum">      51</span>              :     return json;</span>
<span id="L52"><span class="lineNum">      52</span>              :   }</span>
<span id="L53"><span class="lineNum">      53</span>              : }</span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span>              : // ignore_for_file: constant_identifier_names</span>
<span id="L56"><span class="lineNum">      56</span>              : /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/DOP+-+API+SPEC#Metadata-type</span>
<span id="L57"><span class="lineNum">      57</span>              : enum MetadataType {</span>
<span id="L58"><span class="lineNum">      58</span>              :   ACCOMMODATIONS_ID('ACCOMMODATIONS_ID'),</span>
<span id="L59"><span class="lineNum">      59</span>              :   APPROVED_FLOW_TYPE('APPROVED_FLOW_TYPE'),</span>
<span id="L60"><span class="lineNum">      60</span>              :   AUTO_PAYMENT_BANK('AUTO_PAYMENT_BANK'),</span>
<span id="L61"><span class="lineNum">      61</span>              :   BANK('BANK'),</span>
<span id="L62"><span class="lineNum">      62</span>              :   BANK_BRANCH('BANK_BRANCH'),</span>
<span id="L63"><span class="lineNum">      63</span>              :   BANK_BRAND('BANK_BRAND'),</span>
<span id="L64"><span class="lineNum">      64</span>              :   BANK_PROVINCE('BANK_PROVINCE'),</span>
<span id="L65"><span class="lineNum">      65</span>              :   BRANCH('BRANCH'),</span>
<span id="L66"><span class="lineNum">      66</span>              :   BRANCH_DISTRICT('BRANCH_DISTRICT'),</span>
<span id="L67"><span class="lineNum">      67</span>              :   BRANCH_PROVINCE('BRANCH_PROVINCE'),</span>
<span id="L68"><span class="lineNum">      68</span>              :   BRAND_TRADE('BRAND_TRADE'),</span>
<span id="L69"><span class="lineNum">      69</span>              :   CARD_DELIVERY_BRANCH('CARD_DELIVERY_BRANCH'),</span>
<span id="L70"><span class="lineNum">      70</span>              :   CARD_DELIVERY_TYPE('CARD_DELIVERY_TYPE'),</span>
<span id="L71"><span class="lineNum">      71</span>              :   CARD_DESIGN('CARD_DESIGN'),</span>
<span id="L72"><span class="lineNum">      72</span>              :   CARD_DESIGN_GROUP('CARD_DESIGN_GROUP'),</span>
<span id="L73"><span class="lineNum">      73</span>              :   CARD_ISSUE_PLACE('CARD_ISSUE_PLACE'),</span>
<span id="L74"><span class="lineNum">      74</span>              :   CARD_OPEN_PURPOSE('CARD_OPEN_PURPOSE'),</span>
<span id="L75"><span class="lineNum">      75</span>              :   CARD_TYPE('CARD_TYPE'),</span>
<span id="L76"><span class="lineNum">      76</span>              :   CAREER_ID('CAREER_ID'),</span>
<span id="L77"><span class="lineNum">      77</span>              :   COMPANY_TYPE('COMPANY_TYPE'),</span>
<span id="L78"><span class="lineNum">      78</span>              :   COMPANY_TYPE_ID('COMPANY_TYPE_ID'),</span>
<span id="L79"><span class="lineNum">      79</span>              :   CONTACT_TIME_RANGE('CONTACT_TIME_RANGE'),</span>
<span id="L80"><span class="lineNum">      80</span>              :   CREDIT_CARD_CATEGORY('CREDIT_CARD_CATEGORY'),</span>
<span id="L81"><span class="lineNum">      81</span>              :   DELIVERY_CARD_ADDR('DELIVERY_CARD_ADDR'),</span>
<span id="L82"><span class="lineNum">      82</span>              :   DISTRICT('DISTRICT'),</span>
<span id="L83"><span class="lineNum">      83</span>              :   DUE_DATE('DUE_DATE'),</span>
<span id="L84"><span class="lineNum">      84</span>              :   EDUCATION('EDUCATION'),</span>
<span id="L85"><span class="lineNum">      85</span>              :   EDUCATION_ID('EDUCATION_ID'),</span>
<span id="L86"><span class="lineNum">      86</span>              :   EMPLOYMENT('EMPLOYMENT'),</span>
<span id="L87"><span class="lineNum">      87</span>              :   EMPLOYMENT_CAREER('EMPLOYMENT_CAREER'),</span>
<span id="L88"><span class="lineNum">      88</span>              :   EMPLOYMENT_POS('EMPLOYMENT_POS'),</span>
<span id="L89"><span class="lineNum">      89</span>              :   EMPLOYMENT_STATUS('EMPLOYMENT_STATUS'),</span>
<span id="L90"><span class="lineNum">      90</span>              :   EMPLOYMENT_TYPE('EMPLOYMENT_TYPE'),</span>
<span id="L91"><span class="lineNum">      91</span>              :   ETHNIC('ETHNIC'),</span>
<span id="L92"><span class="lineNum">      92</span>              :   EXTRA_SERVICE('EXTRA_SERVICE'),</span>
<span id="L93"><span class="lineNum">      93</span>              :   FEEDBACK_USER_INSIGHT('FEEDBACK_USER_INSIGHT'),</span>
<span id="L94"><span class="lineNum">      94</span>              :   GENDER('GENDER'),</span>
<span id="L95"><span class="lineNum">      95</span>              :   HOUSE_OWNERSHIP('HOUSE_OWNERSHIP'),</span>
<span id="L96"><span class="lineNum">      96</span>              :   ID_CARD_TYPE('ID_CARD_TYPE'),</span>
<span id="L97"><span class="lineNum">      97</span>              :   LABOR_CONTRACT('LABOR_CONTRACT'),</span>
<span id="L98"><span class="lineNum">      98</span>              :   LOAN_CATEGORY('LOAN_CATEGORY'),</span>
<span id="L99"><span class="lineNum">      99</span>              :   LOAN_PURPOSE('LOAN_PURPOSE'),</span>
<span id="L100"><span class="lineNum">     100</span>              :   LOAN_PURPOSE_ID('LOAN_PURPOSE_ID'),</span>
<span id="L101"><span class="lineNum">     101</span>              :   MAIL_ADDRESS_ID('MAIL_ADDRESS_ID'),</span>
<span id="L102"><span class="lineNum">     102</span>              :   MARITAL('MARITAL'),</span>
<span id="L103"><span class="lineNum">     103</span>              :   MARRIED_ID('MARRIED_ID'),</span>
<span id="L104"><span class="lineNum">     104</span>              :   NATIONALITY('NATIONALITY'),</span>
<span id="L105"><span class="lineNum">     105</span>              :   NB_LENDER_RELATIONSHIP('NB_LENDER_RELATIONSHIP'),</span>
<span id="L106"><span class="lineNum">     106</span>              :   PAYMENT_DATE('PAYMENT_DATE'),</span>
<span id="L107"><span class="lineNum">     107</span>              :   PAYMENT_TYPE('PAYMENT_TYPE'),</span>
<span id="L108"><span class="lineNum">     108</span>              :   PIN_MAILER('PIN_MAILER'),</span>
<span id="L109"><span class="lineNum">     109</span>              :   POSITION_ID('POSITION_ID'),</span>
<span id="L110"><span class="lineNum">     110</span>              :   PROVINCE('PROVINCE'),</span>
<span id="L111"><span class="lineNum">     111</span>              :   REG_ADDRESS_STATUS('REG_ADDRESS_STATUS'),</span>
<span id="L112"><span class="lineNum">     112</span>              :   RELATIONSHIP('RELATIONSHIP'),</span>
<span id="L113"><span class="lineNum">     113</span>              :   RELATIONSHIP_1('RELATIONSHIP_1'),</span>
<span id="L114"><span class="lineNum">     114</span>              :   RELATIONSHIP_2('RELATIONSHIP_2'),</span>
<span id="L115"><span class="lineNum">     115</span>              :   RELATIONSHIP_EMPLOYEE('RELATIONSHIP_EMPLOYEE'),</span>
<span id="L116"><span class="lineNum">     116</span>              :   RELATIONSHIP_STUDENT('RELATIONSHIP_STUDENT'),</span>
<span id="L117"><span class="lineNum">     117</span>              :   RESIDENTIAL_PERIOD('RESIDENTIAL_PERIOD'),</span>
<span id="L118"><span class="lineNum">     118</span>              :   SALE_OFFICE('SALE_OFFICE'),</span>
<span id="L119"><span class="lineNum">     119</span>              :   SECURITY_QUESTION('SECURITY_QUESTION'),</span>
<span id="L120"><span class="lineNum">     120</span>              :   STATEMENT_DATE('STATEMENT_DATE'),</span>
<span id="L121"><span class="lineNum">     121</span>              :   SUBSCRIBE_CHANNEL('SUBSCRIBE_CHANNEL'),</span>
<span id="L122"><span class="lineNum">     122</span>              :   TITLE('TITLE'),</span>
<span id="L123"><span class="lineNum">     123</span>              :   WARD('WARD'),</span>
<span id="L124"><span class="lineNum">     124</span>              :   WORKING_ADDRESS('WORKING_ADDRESS'),</span>
<span id="L125"><span class="lineNum">     125</span>              :   WORKING_TYPE_ID('WORKING_TYPE_ID'),</span>
<span id="L126"><span class="lineNum">     126</span>              :   ACQUISITION_REWARD('ACQUISITION_REWARD'),</span>
<span id="L127"><span class="lineNum">     127</span>              :   ACQUISITION_REWARD_TC('ACQUISITION_REWARD_TC');</span>
<span id="L128"><span class="lineNum">     128</span>              : </span>
<span id="L129"><span class="lineNum">     129</span>              :   final String value;</span>
<span id="L130"><span class="lineNum">     130</span>              : </span>
<span id="L131"><span class="lineNum">     131</span>              :   const MetadataType(this.value);</span>
<span id="L132"><span class="lineNum">     132</span>              : </span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :   static MetadataType? byValue(String? value) {</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :     return MetadataType.values.firstWhereOrNull((MetadataType type) =&gt; type.value == value);</span></span>
<span id="L135"><span class="lineNum">     135</span>              :   }</span>
<span id="L136"><span class="lineNum">     136</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
