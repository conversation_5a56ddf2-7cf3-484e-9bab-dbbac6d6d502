<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/dop_native/e_sign_state_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response/dop_native">lib/data/response/dop_native</a> - e_sign_state_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">13</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : class ESignStateEntity extends BaseEntity {</span>
<span id="L6"><span class="lineNum">       6</span>              :   /// Refer to:https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-esign-state</span>
<span id="L7"><span class="lineNum">       7</span>              :   static const String statusReady = 'ready';</span>
<span id="L8"><span class="lineNum">       8</span>              :   static const String statusPending = 'pending';</span>
<span id="L9"><span class="lineNum">       9</span>              :   static const String statusSigned = 'signed';</span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span>              :   final String? status;</span>
<span id="L12"><span class="lineNum">      12</span>              :   final String? renderedUrl;</span>
<span id="L13"><span class="lineNum">      13</span>              :   final String? signedUrl;</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaUNC">           0 :   ESignStateEntity.unserializable()</span></span>
<span id="L16"><span class="lineNum">      16</span>              :       : status = null,</span>
<span id="L17"><span class="lineNum">      17</span>              :         renderedUrl = null,</span>
<span id="L18"><span class="lineNum">      18</span>              :         signedUrl = null,</span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaUNC">           0 :   ESignStateEntity.fromBaseResponse(BaseResponse super.response)</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaUNC">           0 :       : status = response.data?['status'] as String?,</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :         renderedUrl = response.data?['rendered_url'] as String?,</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :         signedUrl = response.data?['signed_url'] as String?,</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L26"><span class="lineNum">      26</span>              : </span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L28"><span class="lineNum">      28</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L29"><span class="lineNum">      29</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :     json.addAll(&lt;String, dynamic&gt;{</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :       'status': status,</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :       'rendered_url': renderedUrl,</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :       'signed_url': signedUrl,</span></span>
<span id="L34"><span class="lineNum">      34</span>              :     });</span>
<span id="L35"><span class="lineNum">      35</span>              :     return json;</span>
<span id="L36"><span class="lineNum">      36</span>              :   }</span>
<span id="L37"><span class="lineNum">      37</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
