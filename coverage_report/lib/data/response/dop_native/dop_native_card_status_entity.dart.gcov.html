<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/dop_native/dop_native_card_status_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response/dop_native">lib/data/response/dop_native</a> - dop_native_card_status_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">33</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import 'dop_native_pos_limit_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : class DOPNativeCardStatusEntity extends BaseEntity {</span>
<span id="L8"><span class="lineNum">       8</span>              :   final DOPNativeCardActivationStatus? activationStatus;</span>
<span id="L9"><span class="lineNum">       9</span>              :   final bool? canActivateCard;</span>
<span id="L10"><span class="lineNum">      10</span>              :   final bool? consentLinkCard;</span>
<span id="L11"><span class="lineNum">      11</span>              :   final bool? isOfflineMerchant;</span>
<span id="L12"><span class="lineNum">      12</span>              :   final DOPNativePosLimitEntity? posLimit;</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaUNC">           0 :   DOPNativeCardStatusEntity({</span></span>
<span id="L15"><span class="lineNum">      15</span>              :     this.activationStatus,</span>
<span id="L16"><span class="lineNum">      16</span>              :     this.canActivateCard,</span>
<span id="L17"><span class="lineNum">      17</span>              :     this.consentLinkCard,</span>
<span id="L18"><span class="lineNum">      18</span>              :     this.isOfflineMerchant,</span>
<span id="L19"><span class="lineNum">      19</span>              :     this.posLimit,</span>
<span id="L20"><span class="lineNum">      20</span>              :   });</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaUNC">           0 :   DOPNativeCardStatusEntity.unserializable()</span></span>
<span id="L23"><span class="lineNum">      23</span>              :       : activationStatus = null,</span>
<span id="L24"><span class="lineNum">      24</span>              :         canActivateCard = null,</span>
<span id="L25"><span class="lineNum">      25</span>              :         consentLinkCard = null,</span>
<span id="L26"><span class="lineNum">      26</span>              :         isOfflineMerchant = null,</span>
<span id="L27"><span class="lineNum">      27</span>              :         posLimit = null,</span>
<span id="L28"><span class="lineNum">      28</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   DOPNativeCardStatusEntity.fromBaseResponse(BaseResponse super.baseResponse)</span></span>
<span id="L31"><span class="lineNum">      31</span> <span class="tlaUNC">           0 :       : activationStatus = DOPNativeCardActivationStatus.fromValue(</span></span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :             baseResponse.data?['activation_status'] as String?),</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :         canActivateCard = baseResponse.data?['can_activate_card'] as bool?,</span></span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :         consentLinkCard = baseResponse.data?['consent_link_card'] as bool?,</span></span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :         isOfflineMerchant = baseResponse.data?['is_offline_merchant'] as bool?,</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :         posLimit = baseResponse.data?['pos_limit'] != null</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :             ? DOPNativePosLimitEntity.fromJson(baseResponse.data?['pos_limit'])</span></span>
<span id="L38"><span class="lineNum">      38</span>              :             : null,</span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L42"><span class="lineNum">      42</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :     json.addAll(&lt;String, dynamic&gt;{</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :       'activation_status': activationStatus?.value,</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :       'can_activate_card': canActivateCard,</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :       'consent_link_card': consentLinkCard,</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :       'is_offline_merchant': isOfflineMerchant,</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :       'pos_limit': posLimit?.toJson(),</span></span>
<span id="L50"><span class="lineNum">      50</span>              :     });</span>
<span id="L51"><span class="lineNum">      51</span>              :     return json;</span>
<span id="L52"><span class="lineNum">      52</span>              :   }</span>
<span id="L53"><span class="lineNum">      53</span>              : }</span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span>              : // Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3580264451/E-Success#GET-Card-status</span>
<span id="L56"><span class="lineNum">      56</span>              : enum DOPNativeCardActivationStatus {</span>
<span id="L57"><span class="lineNum">      57</span>              :   activated('activated'),</span>
<span id="L58"><span class="lineNum">      58</span>              :   activateFailed('activate_failed'),</span>
<span id="L59"><span class="lineNum">      59</span>              :   activateTimeout('activate_timeout'),</span>
<span id="L60"><span class="lineNum">      60</span>              :   changeLimitTimeout('change_limit_timeout'),</span>
<span id="L61"><span class="lineNum">      61</span>              :   changeLimitFailed('change_limit_failed'),</span>
<span id="L62"><span class="lineNum">      62</span>              :   incorrectOtp('incorrect_otp'),</span>
<span id="L63"><span class="lineNum">      63</span>              :   lost('lost'),</span>
<span id="L64"><span class="lineNum">      64</span>              :   notActivated('not_activated'),</span>
<span id="L65"><span class="lineNum">      65</span>              :   permanentBlocked('permanent_blocked'),</span>
<span id="L66"><span class="lineNum">      66</span>              :   temporaryBlocked('temporary_blocked'),</span>
<span id="L67"><span class="lineNum">      67</span>              :   lockCard('lock_card'),</span>
<span id="L68"><span class="lineNum">      68</span>              :   empty(''),</span>
<span id="L69"><span class="lineNum">      69</span>              : </span>
<span id="L70"><span class="lineNum">      70</span>              :   /// This status is defined for using when the activationStatus from API response is difference with all.</span>
<span id="L71"><span class="lineNum">      71</span>              :   unknown('unknown');</span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span>              :   final String value;</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span>              :   const DOPNativeCardActivationStatus(this.value);</span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :   static DOPNativeCardActivationStatus fromValue(String? value) {</span></span>
<span id="L78"><span class="lineNum">      78</span>              :     switch (value) {</span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :       case 'activated':</span></span>
<span id="L80"><span class="lineNum">      80</span>              :         return DOPNativeCardActivationStatus.activated;</span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :       case 'activate_failed':</span></span>
<span id="L82"><span class="lineNum">      82</span>              :         return DOPNativeCardActivationStatus.activateFailed;</span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :       case 'activate_timeout':</span></span>
<span id="L84"><span class="lineNum">      84</span>              :         return DOPNativeCardActivationStatus.activateTimeout;</span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :       case 'change_limit_timeout':</span></span>
<span id="L86"><span class="lineNum">      86</span>              :         return DOPNativeCardActivationStatus.changeLimitTimeout;</span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :       case 'change_limit_failed':</span></span>
<span id="L88"><span class="lineNum">      88</span>              :         return DOPNativeCardActivationStatus.changeLimitFailed;</span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :       case 'incorrect_otp':</span></span>
<span id="L90"><span class="lineNum">      90</span>              :         return DOPNativeCardActivationStatus.incorrectOtp;</span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :       case 'lost':</span></span>
<span id="L92"><span class="lineNum">      92</span>              :         return DOPNativeCardActivationStatus.lost;</span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :       case 'not_activated':</span></span>
<span id="L94"><span class="lineNum">      94</span>              :         return DOPNativeCardActivationStatus.notActivated;</span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :       case 'permanent_blocked':</span></span>
<span id="L96"><span class="lineNum">      96</span>              :         return DOPNativeCardActivationStatus.permanentBlocked;</span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :       case 'temporary_blocked':</span></span>
<span id="L98"><span class="lineNum">      98</span>              :         return DOPNativeCardActivationStatus.temporaryBlocked;</span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :       case 'lock_card':</span></span>
<span id="L100"><span class="lineNum">     100</span>              :         return DOPNativeCardActivationStatus.lockCard;</span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :       case '':</span></span>
<span id="L102"><span class="lineNum">     102</span>              :         return DOPNativeCardActivationStatus.empty;</span>
<span id="L103"><span class="lineNum">     103</span>              :       default:</span>
<span id="L104"><span class="lineNum">     104</span>              :         return DOPNativeCardActivationStatus.unknown;</span>
<span id="L105"><span class="lineNum">     105</span>              :     }</span>
<span id="L106"><span class="lineNum">     106</span>              :   }</span>
<span id="L107"><span class="lineNum">     107</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
