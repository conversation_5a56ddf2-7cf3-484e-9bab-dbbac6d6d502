<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/dop_native/dop_native_contact_info_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response/dop_native">lib/data/response/dop_native</a> - dop_native_contact_info_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">1.7&nbsp;%</td>
            <td class="headerCovTableEntry">59</td>
            <td class="headerCovTableEntry">1</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : class DOPNativeContactInfoEntity {</span>
<span id="L2"><span class="lineNum">       2</span>              :   final String? familyAddress;</span>
<span id="L3"><span class="lineNum">       3</span>              :   final String? familyBookAddressDistId;</span>
<span id="L4"><span class="lineNum">       4</span>              :   final String? familyBookAddressProvinceId;</span>
<span id="L5"><span class="lineNum">       5</span>              :   final String? familyBookAddressWardId;</span>
<span id="L6"><span class="lineNum">       6</span>              :   final String? curAddress;</span>
<span id="L7"><span class="lineNum">       7</span>              :   final String? curAddressDistId;</span>
<span id="L8"><span class="lineNum">       8</span>              :   final String? curAddressProvinceId;</span>
<span id="L9"><span class="lineNum">       9</span>              :   final String? curAddressWardId;</span>
<span id="L10"><span class="lineNum">      10</span>              :   final String? companyAddress;</span>
<span id="L11"><span class="lineNum">      11</span>              :   final String? companyAddressDistId;</span>
<span id="L12"><span class="lineNum">      12</span>              :   final String? companyAddressProvinceId;</span>
<span id="L13"><span class="lineNum">      13</span>              :   final String? companyAddressWardId;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final String? companyName;</span>
<span id="L15"><span class="lineNum">      15</span>              :   final bool? isZaloCurrentPhoneNumber;</span>
<span id="L16"><span class="lineNum">      16</span>              :   final String? pickupCardAddress;</span>
<span id="L17"><span class="lineNum">      17</span>              :   final String? zaloPhoneNumber;</span>
<span id="L18"><span class="lineNum">      18</span>              :   final String? subscribeChannel;</span>
<span id="L19"><span class="lineNum">      19</span>              : </span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaGNC">           4 :   const DOPNativeContactInfoEntity({</span></span>
<span id="L21"><span class="lineNum">      21</span>              :     this.familyAddress,</span>
<span id="L22"><span class="lineNum">      22</span>              :     this.familyBookAddressDistId,</span>
<span id="L23"><span class="lineNum">      23</span>              :     this.familyBookAddressProvinceId,</span>
<span id="L24"><span class="lineNum">      24</span>              :     this.familyBookAddressWardId,</span>
<span id="L25"><span class="lineNum">      25</span>              :     this.curAddress,</span>
<span id="L26"><span class="lineNum">      26</span>              :     this.curAddressDistId,</span>
<span id="L27"><span class="lineNum">      27</span>              :     this.curAddressProvinceId,</span>
<span id="L28"><span class="lineNum">      28</span>              :     this.curAddressWardId,</span>
<span id="L29"><span class="lineNum">      29</span>              :     this.companyAddress,</span>
<span id="L30"><span class="lineNum">      30</span>              :     this.companyAddressDistId,</span>
<span id="L31"><span class="lineNum">      31</span>              :     this.companyAddressProvinceId,</span>
<span id="L32"><span class="lineNum">      32</span>              :     this.companyAddressWardId,</span>
<span id="L33"><span class="lineNum">      33</span>              :     this.companyName,</span>
<span id="L34"><span class="lineNum">      34</span>              :     this.isZaloCurrentPhoneNumber,</span>
<span id="L35"><span class="lineNum">      35</span>              :     this.pickupCardAddress,</span>
<span id="L36"><span class="lineNum">      36</span>              :     this.zaloPhoneNumber,</span>
<span id="L37"><span class="lineNum">      37</span>              :     this.subscribeChannel,</span>
<span id="L38"><span class="lineNum">      38</span>              :   });</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :   DOPNativeContactInfoEntity.fromJson(Map&lt;String, dynamic&gt; json)</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :       : familyAddress = json['familyAddress'] as String?,</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :         familyBookAddressDistId = json['familyBookAddressDistId'] as String?,</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :         familyBookAddressProvinceId = json['familyBookAddressProvinceId'] as String?,</span></span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :         familyBookAddressWardId = json['familyBookAddressWardId'] as String?,</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :         curAddress = json['curAddress'] as String?,</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :         curAddressDistId = json['curAddressDistId'] as String?,</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :         curAddressProvinceId = json['curAddressProvinceId'] as String?,</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :         curAddressWardId = json['curAddressWardId'] as String?,</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :         companyAddress = json['companyAddress'] as String?,</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :         companyAddressDistId = json['companyAddressDistId'] as String?,</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :         companyAddressProvinceId = json['companyAddressProvinceId'] as String?,</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :         companyAddressWardId = json['companyAddressWardId'] as String?,</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :         companyName = json['companyName'] as String?,</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :         isZaloCurrentPhoneNumber = json['isZaloCurrentPhoneNumber'] as bool?,</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :         pickupCardAddress = json['pickupCardAddress'] as String?,</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :         zaloPhoneNumber = json['zaloPhoneNumber'] as String?,</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :         subscribeChannel = json['subscribeChannel'] as String?;</span></span>
<span id="L58"><span class="lineNum">      58</span>              : </span>
<span id="L59"><span class="lineNum">      59</span>              :   /// Returned json should exclude null value field</span>
<span id="L60"><span class="lineNum">      60</span>              :   /// Refer: https://trustingsocial.slack.com/archives/C06BBSRR99P/p1714891289576959</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt;? toJson() {</span></span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = &lt;String, dynamic&gt;{</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :       'familyAddress': familyAddress,</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :       'familyBookAddressDistId': familyBookAddressDistId,</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :       'familyBookAddressProvinceId': familyBookAddressProvinceId,</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :       'familyBookAddressWardId': familyBookAddressWardId,</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :       'curAddress': curAddress,</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :       'curAddressDistId': curAddressDistId,</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :       'curAddressProvinceId': curAddressProvinceId,</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :       'curAddressWardId': curAddressWardId,</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :       'companyAddress': companyAddress,</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :       'companyAddressDistId': companyAddressDistId,</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :       'companyAddressProvinceId': companyAddressProvinceId,</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :       'companyAddressWardId': companyAddressWardId,</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :       'companyName': companyName,</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :       'isZaloCurrentPhoneNumber': isZaloCurrentPhoneNumber,</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :       'pickupCardAddress': pickupCardAddress,</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :       'zaloPhoneNumber': zaloPhoneNumber?.isEmpty == true ? null : zaloPhoneNumber,</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :       'subscribeChannel': subscribeChannel,</span></span>
<span id="L80"><span class="lineNum">      80</span>              :     };</span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :     json.removeWhere((_, dynamic value) {</span></span>
<span id="L83"><span class="lineNum">      83</span>              :       return value == null;</span>
<span id="L84"><span class="lineNum">      84</span>              :     });</span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :     if (json.isEmpty) {</span></span>
<span id="L86"><span class="lineNum">      86</span>              :       return null;</span>
<span id="L87"><span class="lineNum">      87</span>              :     }</span>
<span id="L88"><span class="lineNum">      88</span>              :     return json;</span>
<span id="L89"><span class="lineNum">      89</span>              :   }</span>
<span id="L90"><span class="lineNum">      90</span>              : </span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :   DOPNativeContactInfoEntity copyWith({</span></span>
<span id="L92"><span class="lineNum">      92</span>              :     String? familyAddress,</span>
<span id="L93"><span class="lineNum">      93</span>              :     String? familyBookAddressDistId,</span>
<span id="L94"><span class="lineNum">      94</span>              :     String? familyBookAddressProvinceId,</span>
<span id="L95"><span class="lineNum">      95</span>              :     String? familyBookAddressWardId,</span>
<span id="L96"><span class="lineNum">      96</span>              :     String? curAddress,</span>
<span id="L97"><span class="lineNum">      97</span>              :     String? curAddressDistId,</span>
<span id="L98"><span class="lineNum">      98</span>              :     String? curAddressProvinceId,</span>
<span id="L99"><span class="lineNum">      99</span>              :     String? curAddressWardId,</span>
<span id="L100"><span class="lineNum">     100</span>              :     String? companyAddress,</span>
<span id="L101"><span class="lineNum">     101</span>              :     String? companyAddressDistId,</span>
<span id="L102"><span class="lineNum">     102</span>              :     String? companyAddressProvinceId,</span>
<span id="L103"><span class="lineNum">     103</span>              :     String? companyAddressWardId,</span>
<span id="L104"><span class="lineNum">     104</span>              :     String? companyName,</span>
<span id="L105"><span class="lineNum">     105</span>              :     bool? isZaloCurrentPhoneNumber,</span>
<span id="L106"><span class="lineNum">     106</span>              :     String? pickupCardAddress,</span>
<span id="L107"><span class="lineNum">     107</span>              :     String? zaloPhoneNumber,</span>
<span id="L108"><span class="lineNum">     108</span>              :     String? subscribeChannel,</span>
<span id="L109"><span class="lineNum">     109</span>              :   }) {</span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :     return DOPNativeContactInfoEntity(</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :       familyAddress: familyAddress ?? this.familyAddress,</span></span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :       familyBookAddressDistId: familyBookAddressDistId ?? this.familyBookAddressDistId,</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :       familyBookAddressProvinceId: familyBookAddressProvinceId ?? this.familyBookAddressProvinceId,</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :       familyBookAddressWardId: familyBookAddressWardId ?? this.familyBookAddressWardId,</span></span>
<span id="L115"><span class="lineNum">     115</span> <span class="tlaUNC">           0 :       curAddress: curAddress ?? this.curAddress,</span></span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :       curAddressDistId: curAddressDistId ?? this.curAddressDistId,</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :       curAddressProvinceId: curAddressProvinceId ?? this.curAddressProvinceId,</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :       curAddressWardId: curAddressWardId ?? this.curAddressWardId,</span></span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :       companyAddress: companyAddress ?? this.companyAddress,</span></span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :       companyAddressDistId: companyAddressDistId ?? this.companyAddressDistId,</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :       companyAddressProvinceId: companyAddressProvinceId ?? this.companyAddressProvinceId,</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :       companyAddressWardId: companyAddressWardId ?? this.companyAddressWardId,</span></span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :       companyName: companyName ?? this.companyName,</span></span>
<span id="L124"><span class="lineNum">     124</span> <span class="tlaUNC">           0 :       isZaloCurrentPhoneNumber: isZaloCurrentPhoneNumber ?? this.isZaloCurrentPhoneNumber,</span></span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :       pickupCardAddress: pickupCardAddress ?? this.pickupCardAddress,</span></span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :       zaloPhoneNumber: zaloPhoneNumber ?? this.zaloPhoneNumber,</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :       subscribeChannel: subscribeChannel ?? this.subscribeChannel,</span></span>
<span id="L128"><span class="lineNum">     128</span>              :     );</span>
<span id="L129"><span class="lineNum">     129</span>              :   }</span>
<span id="L130"><span class="lineNum">     130</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
