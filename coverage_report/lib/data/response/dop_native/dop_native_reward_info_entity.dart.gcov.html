<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/dop_native/dop_native_reward_info_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response/dop_native">lib/data/response/dop_native</a> - dop_native_reward_info_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">8</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : class DOPNativeRewardInfoEntity {</span>
<span id="L2"><span class="lineNum">       2</span>              :   final String? rewardID;</span>
<span id="L3"><span class="lineNum">       3</span>              : </span>
<span id="L4"><span class="lineNum">       4</span> <span class="tlaUNC">           0 :   const DOPNativeRewardInfoEntity({</span></span>
<span id="L5"><span class="lineNum">       5</span>              :     this.rewardID,</span>
<span id="L6"><span class="lineNum">       6</span>              :   });</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span> <span class="tlaUNC">           0 :   DOPNativeRewardInfoEntity.fromJson(Map&lt;String, dynamic&gt; json)</span></span>
<span id="L9"><span class="lineNum">       9</span> <span class="tlaUNC">           0 :       : rewardID = json['rewardID'] as String?;</span></span>
<span id="L10"><span class="lineNum">      10</span>              : </span>
<span id="L11"><span class="lineNum">      11</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt;? toJson() {</span></span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = &lt;String, dynamic&gt;{</span></span>
<span id="L13"><span class="lineNum">      13</span> <span class="tlaUNC">           0 :       'rewardID': rewardID,</span></span>
<span id="L14"><span class="lineNum">      14</span>              :     };</span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaUNC">           0 :     json.removeWhere((_, dynamic value) {</span></span>
<span id="L16"><span class="lineNum">      16</span>              :       return value == null;</span>
<span id="L17"><span class="lineNum">      17</span>              :     });</span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaUNC">           0 :     if (json.isEmpty) {</span></span>
<span id="L19"><span class="lineNum">      19</span>              :       return null;</span>
<span id="L20"><span class="lineNum">      20</span>              :     }</span>
<span id="L21"><span class="lineNum">      21</span>              :     return json;</span>
<span id="L22"><span class="lineNum">      22</span>              :   }</span>
<span id="L23"><span class="lineNum">      23</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
