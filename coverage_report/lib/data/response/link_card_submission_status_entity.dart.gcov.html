<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/link_card_submission_status_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - link_card_submission_status_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">18</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import 'action_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : class LinkCardSubmissionStatusEntity extends BaseEntity {</span>
<span id="L8"><span class="lineNum">       8</span>              :   static const String statusProcessing = 'processing';</span>
<span id="L9"><span class="lineNum">       9</span>              :   static const String statusFailed = 'failed';</span>
<span id="L10"><span class="lineNum">      10</span>              :   static const String statusSucceeded = 'succeeded';</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              :   /// INQUIRY STATUS</span>
<span id="L13"><span class="lineNum">      13</span>              :   /// Ref: https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3296461984/Manual+Card+Link+-+Response+code</span>
<span id="L14"><span class="lineNum">      14</span>              :   static const String verdictLinkCardFailure = 'link_card_failure';</span>
<span id="L15"><span class="lineNum">      15</span>              :   static const String verdictDuplicatedLinkRequest = 'duplicated_link_request';</span>
<span id="L16"><span class="lineNum">      16</span>              :   static const String verdictLinkCardInvalidParameters = 'link_card_invalid_parameters';</span>
<span id="L17"><span class="lineNum">      17</span>              :   static const String verdictLinkCardLinkRequestNotExists = 'link_card_link_request_not_exists';</span>
<span id="L18"><span class="lineNum">      18</span>              :   static const String verdictLinkCardLinkTimeout = 'link_card_link_timeout';</span>
<span id="L19"><span class="lineNum">      19</span>              :   static const String verdictLinkCardInvalidBankCode = 'link_card_invalid_bank_code';</span>
<span id="L20"><span class="lineNum">      20</span>              :   static const String verdictLinkCardBankProductNotSupported =</span>
<span id="L21"><span class="lineNum">      21</span>              :       'link_card_bank_product_not_supported';</span>
<span id="L22"><span class="lineNum">      22</span>              :   static const String verdictLinkCardAlreadyLinked = 'link_card_already_linked';</span>
<span id="L23"><span class="lineNum">      23</span>              :   static const String verdictLinkCardInvalidIDNumber = 'link_card_invalid_ic_number';</span>
<span id="L24"><span class="lineNum">      24</span>              :   static const String verdictLinkCardInvalidPhoneNumber = 'link_card_invalid_phone_number';</span>
<span id="L25"><span class="lineNum">      25</span>              :   static const String verdictLinkCardNotFoundLinkInfo = 'link_card_not_found_link_info';</span>
<span id="L26"><span class="lineNum">      26</span>              :   static const String verdictLinkCardNotExists = 'link_card_not_exists';</span>
<span id="L27"><span class="lineNum">      27</span>              :   static const String verdictLinkCardUnsupportedCard = 'link_card_unsupported_card';</span>
<span id="L28"><span class="lineNum">      28</span>              :   static const String verdictLinkCardTooManyValidCard = 'link_card_too_many_valid_card';</span>
<span id="L29"><span class="lineNum">      29</span>              :   static const String verdictLinkCardInvalidCardStatus = 'link_card_invalid_card_status';</span>
<span id="L30"><span class="lineNum">      30</span>              :   static const String verdictLinkCardOnboardingRequestFailed =</span>
<span id="L31"><span class="lineNum">      31</span>              :       'link_card_onboarding_request_failed';</span>
<span id="L32"><span class="lineNum">      32</span>              :   static const String verdictLinkCardNotFoundOnboardingRequest =</span>
<span id="L33"><span class="lineNum">      33</span>              :       'link_card_not_found_onboarding_request';</span>
<span id="L34"><span class="lineNum">      34</span>              :   static const String verdictLinkCardLinkRequestIsProcessing =</span>
<span id="L35"><span class="lineNum">      35</span>              :       'link_card_link_request_is_processing';</span>
<span id="L36"><span class="lineNum">      36</span>              :   static const String verdictLinkCardPermissionDenied = 'permission_denied';</span>
<span id="L37"><span class="lineNum">      37</span>              :   static const String verdictLinkCardRecordNotFound = 'record_not_found';</span>
<span id="L38"><span class="lineNum">      38</span>              :   static const String verdictLinkCardFailureAll = 'failure';</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span>              :   final ActionEntity? action;</span>
<span id="L41"><span class="lineNum">      41</span>              :   final int? intervalInquiryMs;</span>
<span id="L42"><span class="lineNum">      42</span>              :   final String? linkCardRequestId;</span>
<span id="L43"><span class="lineNum">      43</span>              :   final String? linkCardStatus;</span>
<span id="L44"><span class="lineNum">      44</span>              :   final int? nextRetryIfExitDurationInMinute;</span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :   LinkCardSubmissionStatusEntity({</span></span>
<span id="L47"><span class="lineNum">      47</span>              :     this.action,</span>
<span id="L48"><span class="lineNum">      48</span>              :     this.intervalInquiryMs,</span>
<span id="L49"><span class="lineNum">      49</span>              :     this.linkCardRequestId,</span>
<span id="L50"><span class="lineNum">      50</span>              :     this.linkCardStatus,</span>
<span id="L51"><span class="lineNum">      51</span>              :     this.nextRetryIfExitDurationInMinute,</span>
<span id="L52"><span class="lineNum">      52</span>              :   });</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :   LinkCardSubmissionStatusEntity.unserializable()</span></span>
<span id="L55"><span class="lineNum">      55</span>              :       : action = null,</span>
<span id="L56"><span class="lineNum">      56</span>              :         intervalInquiryMs = null,</span>
<span id="L57"><span class="lineNum">      57</span>              :         linkCardRequestId = null,</span>
<span id="L58"><span class="lineNum">      58</span>              :         linkCardStatus = null,</span>
<span id="L59"><span class="lineNum">      59</span>              :         nextRetryIfExitDurationInMinute = null,</span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :   LinkCardSubmissionStatusEntity.fromBaseResponse(BaseResponse super.response)</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :       : action = (response.data?['action'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :             ? ActionEntity.fromJson(response.data?['action'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L65"><span class="lineNum">      65</span>              :             : null,</span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :         intervalInquiryMs = response.data?['interval_inquiry_ms'] as int?,</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :         linkCardRequestId = response.data?['link_card_request_id'] as String?,</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :         linkCardStatus = response.data?['link_card_status'] as String?,</span></span>
<span id="L69"><span class="lineNum">      69</span>              :         nextRetryIfExitDurationInMinute =</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :             response.data?['next_retry_if_exit_duration_in_minute'] as int?,</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L74"><span class="lineNum">      74</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :     json['action'] = action?.toJson();</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     json['interval_inquiry_ms'] = intervalInquiryMs;</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :     json['link_card_request_id'] = linkCardRequestId;</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :     json['link_card_status'] = linkCardStatus;</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :     json['next_retry_if_exit_duration_in_minute'] = nextRetryIfExitDurationInMinute;</span></span>
<span id="L81"><span class="lineNum">      81</span>              :     return json;</span>
<span id="L82"><span class="lineNum">      82</span>              :   }</span>
<span id="L83"><span class="lineNum">      83</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
