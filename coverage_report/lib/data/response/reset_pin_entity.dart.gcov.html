<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/reset_pin_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - reset_pin_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">22</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import 'ekyc_info_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : class ResetPinEntity extends BaseEntity {</span>
<span id="L8"><span class="lineNum">       8</span>              :   /// if incorrect otp too many times, return status code [CommonHttpClient.LIMIT_EXCEEDED]</span>
<span id="L9"><span class="lineNum">       9</span>              :   /// limit call API (3 times within 24h)</span>
<span id="L10"><span class="lineNum">      10</span>              :   // limit call API with challenge type verify_national_id (5 times / reset PIN request)</span>
<span id="L11"><span class="lineNum">      11</span>              :   // limit call API with challenge type verify_otp (3 times / reset PIN request)</span>
<span id="L12"><span class="lineNum">      12</span>              :   // limit call API with challenge type resend_otp (3 times / verify otp request / reset PIN request)</span>
<span id="L13"><span class="lineNum">      13</span>              :   static const String verdictLimitExceeded = 'limit_exceeded';</span>
<span id="L14"><span class="lineNum">      14</span>              : </span>
<span id="L15"><span class="lineNum">      15</span>              :   ///user with phone_number not found, return status code [CommonHttpClient.NOT_FOUND]</span>
<span id="L16"><span class="lineNum">      16</span>              :   static const String verdictUserNotExisted = 'record_not_found';</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              :   ///request type and type in session_token are not the same, return status code [CommonHttpClient.BAD_REQUEST]</span>
<span id="L19"><span class="lineNum">      19</span>              :   static const String verdictInvalidState = 'invalid_state';</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span>              :   ///national id, otp, phone number, pin not valid format or rule, return status code [CommonHttpClient.BAD_REQUEST]</span>
<span id="L22"><span class="lineNum">      22</span>              :   static const String verdictInvalidParameters = 'invalid_parameters';</span>
<span id="L23"><span class="lineNum">      23</span>              : </span>
<span id="L24"><span class="lineNum">      24</span>              :   static const String verdictInvalidCredential = 'invalid_credential';</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   static const String verdictOneLastTry = 'one_last_try';</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span>              :   static const String verdictExpiredData = 'expired_data';</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span>              :   static const String verdictInvalidPin = 'invalid_pin';</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span>              :   ///user has been locked to reset pin</span>
<span id="L33"><span class="lineNum">      33</span>              :   static const String verdictLockedResource = 'locked_resource';</span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span>              :   ///invalid session token, return status code [CommonHttpClient.INVALID_TOKEN]</span>
<span id="L36"><span class="lineNum">      36</span>              :   static const String verdictInvalidResetPinSession = 'invalid_reset_pin_session';</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span>              :   ///session token is expired, return status code [CommonHttpClient.INVALID_TOKEN]</span>
<span id="L39"><span class="lineNum">      39</span>              :   static const String verdictExpiredResetPinSession = 'expired_reset_pin_session';</span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span>              :   ///internal server error, return status code [CommonHttpClient.INTERNAL_SERVER_ERROR]</span>
<span id="L42"><span class="lineNum">      42</span>              :   static const String verdictFailure = 'failure';</span>
<span id="L43"><span class="lineNum">      43</span>              : </span>
<span id="L44"><span class="lineNum">      44</span>              :   ///reset pin success, return status code [CommonHttpClient.SUCCESS]</span>
<span id="L45"><span class="lineNum">      45</span>              :   static const String verdictSuccess = 'success';</span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span>              :   final String? challengeType;</span>
<span id="L48"><span class="lineNum">      48</span>              :   final int? otpResendSecs;</span>
<span id="L49"><span class="lineNum">      49</span>              :   final int? otpValiditySecs;</span>
<span id="L50"><span class="lineNum">      50</span>              :   final int? retryRemainingCount;</span>
<span id="L51"><span class="lineNum">      51</span>              :   final String? sessionToken;</span>
<span id="L52"><span class="lineNum">      52</span>              :   final EKYCSessionEntity? ekycCredential;</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :   ResetPinEntity({</span></span>
<span id="L55"><span class="lineNum">      55</span>              :     this.challengeType,</span>
<span id="L56"><span class="lineNum">      56</span>              :     this.otpResendSecs,</span>
<span id="L57"><span class="lineNum">      57</span>              :     this.otpValiditySecs,</span>
<span id="L58"><span class="lineNum">      58</span>              :     this.retryRemainingCount,</span>
<span id="L59"><span class="lineNum">      59</span>              :     this.sessionToken,</span>
<span id="L60"><span class="lineNum">      60</span>              :     this.ekycCredential,</span>
<span id="L61"><span class="lineNum">      61</span>              :   });</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :   ResetPinEntity.unSerializable()</span></span>
<span id="L64"><span class="lineNum">      64</span>              :       : challengeType = null,</span>
<span id="L65"><span class="lineNum">      65</span>              :         otpResendSecs = null,</span>
<span id="L66"><span class="lineNum">      66</span>              :         otpValiditySecs = null,</span>
<span id="L67"><span class="lineNum">      67</span>              :         retryRemainingCount = null,</span>
<span id="L68"><span class="lineNum">      68</span>              :         sessionToken = null,</span>
<span id="L69"><span class="lineNum">      69</span>              :         ekycCredential = null,</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :   ResetPinEntity.fromBaseResponse(BaseResponse super.baseResponse)</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :       : challengeType = baseResponse.data?['challenge_type'] as String?,</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :         sessionToken = baseResponse.data?['session_token'] as String?,</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :         otpResendSecs = baseResponse.data?['otp_resend_secs'] as int?,</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :         otpValiditySecs = baseResponse.data?['otp_validity_secs'] as int?,</span></span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :         retryRemainingCount = baseResponse.data?['retry_remaining_count'] as int?,</span></span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :         ekycCredential = (baseResponse.data?['ekyc_credential'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :             ? EKYCSessionEntity.fromBaseResponseForFaceOtp(</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :                 baseResponse, baseResponse.data?['ekyc_credential'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L81"><span class="lineNum">      81</span>              :             : null,</span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L85"><span class="lineNum">      85</span>              :   Map&lt;String, dynamic&gt; toJson() {</span>
<span id="L86"><span class="lineNum">      86</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = super.toJson();</span></span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :     json.addAll(&lt;String, dynamic&gt;{</span></span>
<span id="L88"><span class="lineNum">      88</span> <span class="tlaUNC">           0 :       'challenge_type': challengeType,</span></span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :       'session_token': sessionToken,</span></span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :       'otp_resend_secs': otpResendSecs,</span></span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :       'otp_validity_secs': otpValiditySecs,</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :       'retry_remaining_count': retryRemainingCount,</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :       'ekyc_credential': ekycCredential?.toJson(),</span></span>
<span id="L94"><span class="lineNum">      94</span>              :     });</span>
<span id="L95"><span class="lineNum">      95</span>              :     return json;</span>
<span id="L96"><span class="lineNum">      96</span>              :   }</span>
<span id="L97"><span class="lineNum">      97</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
