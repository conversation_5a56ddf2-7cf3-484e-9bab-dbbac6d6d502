<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/campaign_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - campaign_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">36</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/util/functions.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : </span>
<span id="L3"><span class="lineNum">       3</span>              : import 'action_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'offer_entity.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : class CampaignEntity {</span>
<span id="L7"><span class="lineNum">       7</span>              :   final ActionEntity? action;</span>
<span id="L8"><span class="lineNum">       8</span>              :   final ActionEntity? earnAction;</span>
<span id="L9"><span class="lineNum">       9</span>              :   final String? banner;</span>
<span id="L10"><span class="lineNum">      10</span>              :   final String? campaignType;</span>
<span id="L11"><span class="lineNum">      11</span>              :   final String? code;</span>
<span id="L12"><span class="lineNum">      12</span>              :   final String? description;</span>
<span id="L13"><span class="lineNum">      13</span>              :   final String? startAt;</span>
<span id="L14"><span class="lineNum">      14</span>              :   final String? endAt;</span>
<span id="L15"><span class="lineNum">      15</span>              :   final String? formattedExpiry;</span>
<span id="L16"><span class="lineNum">      16</span>              :   final String? id;</span>
<span id="L17"><span class="lineNum">      17</span>              :   final String? thumbnail;</span>
<span id="L18"><span class="lineNum">      18</span>              :   final String? title;</span>
<span id="L19"><span class="lineNum">      19</span>              :   final List&lt;OfferEntity&gt;? offers;</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaUNC">           0 :   CampaignEntity({</span></span>
<span id="L22"><span class="lineNum">      22</span>              :     this.action,</span>
<span id="L23"><span class="lineNum">      23</span>              :     this.earnAction,</span>
<span id="L24"><span class="lineNum">      24</span>              :     this.banner,</span>
<span id="L25"><span class="lineNum">      25</span>              :     this.campaignType,</span>
<span id="L26"><span class="lineNum">      26</span>              :     this.code,</span>
<span id="L27"><span class="lineNum">      27</span>              :     this.description,</span>
<span id="L28"><span class="lineNum">      28</span>              :     this.startAt,</span>
<span id="L29"><span class="lineNum">      29</span>              :     this.endAt,</span>
<span id="L30"><span class="lineNum">      30</span>              :     this.formattedExpiry,</span>
<span id="L31"><span class="lineNum">      31</span>              :     this.id,</span>
<span id="L32"><span class="lineNum">      32</span>              :     this.thumbnail,</span>
<span id="L33"><span class="lineNum">      33</span>              :     this.title,</span>
<span id="L34"><span class="lineNum">      34</span>              :     this.offers,</span>
<span id="L35"><span class="lineNum">      35</span>              :   });</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :   DateTime? get startExpireDateTime =&gt; commonUtilFunction.toDateTime(startAt);</span></span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :   DateTime? get endExpireDateTime =&gt; commonUtilFunction.toDateTime(endAt);</span></span>
<span id="L40"><span class="lineNum">      40</span>              : </span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :   CampaignEntity.fromJson(Map&lt;String, dynamic&gt; json)</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :       : action = (json['action'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :             ? ActionEntity.fromJson(json['action'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L44"><span class="lineNum">      44</span>              :             : null,</span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :         earnAction = (json['earn_action'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :             ? ActionEntity.fromJson(json['earn_action'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L47"><span class="lineNum">      47</span>              :             : null,</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :         banner = json['banner'] as String?,</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :         campaignType = json['campaign_type'] as String?,</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :         code = json['code'] as String?,</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :         description = json['description'] as String?,</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :         startAt = json['start_at'] as String?,</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :         endAt = json['end_at'] as String?,</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :         formattedExpiry = json['formatted_expiry'] as String?,</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :         id = json['id'] as String?,</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :         thumbnail = json['thumbnail'] as String?,</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :         offers = (json['offers'] as List&lt;dynamic&gt;?)</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :             ?.map((dynamic e) =&gt; OfferEntity.fromJson(e as Map&lt;String, dynamic&gt;))</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :             .toList(),</span></span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :         title = json['title'] as String?;</span></span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; &lt;String, dynamic&gt;{</span></span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :         'action': action?.toJson(),</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :         'earn_action': earnAction?.toJson(),</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :         'banner': banner,</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :         'campaign_type': campaignType,</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :         'code': code,</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :         'description': description,</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :         'start_at': startAt,</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :         'end_at': endAt,</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :         'formatted_expiry': formattedExpiry,</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :         'id': id,</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :         'thumbnail': thumbnail,</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :         'title': title,</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :         'offers': &lt;String, dynamic&gt;{</span></span>
<span id="L76"><span class="lineNum">      76</span> <span class="tlaUNC">           0 :           'campaigns': offers?.map((OfferEntity v) =&gt; v.toJson()).toList()</span></span>
<span id="L77"><span class="lineNum">      77</span>              :         }</span>
<span id="L78"><span class="lineNum">      78</span>              :       };</span>
<span id="L79"><span class="lineNum">      79</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
