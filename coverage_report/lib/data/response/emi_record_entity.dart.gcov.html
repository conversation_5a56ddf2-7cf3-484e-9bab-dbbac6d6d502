<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/response/emi_record_entity.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/response">lib/data/response</a> - emi_record_entity.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">63</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : import 'emi_contract_entity.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import 'emi_conversion_status_info_entity.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import 'emi_package_entity.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import 'merchant_info_entity.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import 'order_info_entity.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span>              : // Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/3295412375/EMI+conversion+repayment+status+management#EMI-record-status-and-equivalent-status-on-UI</span>
<span id="L13"><span class="lineNum">      13</span>              : enum EmiRecordStatus {</span>
<span id="L14"><span class="lineNum">      14</span>              :   prepared('prepared'),</span>
<span id="L15"><span class="lineNum">      15</span>              :   processing('processing'),</span>
<span id="L16"><span class="lineNum">      16</span>              :   requiresManual('requires_manual'),</span>
<span id="L17"><span class="lineNum">      17</span>              :   undefinedError('undefined_error'),</span>
<span id="L18"><span class="lineNum">      18</span>              :   converted('converted'),</span>
<span id="L19"><span class="lineNum">      19</span>              :   paying('paying'),</span>
<span id="L20"><span class="lineNum">      20</span>              :   paid('paid'),</span>
<span id="L21"><span class="lineNum">      21</span>              :   convertedManual('converted_manual'),</span>
<span id="L22"><span class="lineNum">      22</span>              :   rejected('rejected'),</span>
<span id="L23"><span class="lineNum">      23</span>              :   // The status is defined in PRD</span>
<span id="L24"><span class="lineNum">      24</span>              :   unknown('unknown');</span>
<span id="L25"><span class="lineNum">      25</span>              : </span>
<span id="L26"><span class="lineNum">      26</span>              :   final String value;</span>
<span id="L27"><span class="lineNum">      27</span>              : </span>
<span id="L28"><span class="lineNum">      28</span>              :   const EmiRecordStatus(this.value);</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   static EmiRecordStatus fromValue(String? value) {</span></span>
<span id="L31"><span class="lineNum">      31</span>              :     switch (value) {</span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :       case 'prepared':</span></span>
<span id="L33"><span class="lineNum">      33</span>              :         return EmiRecordStatus.prepared;</span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :       case 'processing':</span></span>
<span id="L35"><span class="lineNum">      35</span>              :         return EmiRecordStatus.processing;</span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :       case 'requires_manual':</span></span>
<span id="L37"><span class="lineNum">      37</span>              :         return EmiRecordStatus.requiresManual;</span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :       case 'undefined_error':</span></span>
<span id="L39"><span class="lineNum">      39</span>              :         return EmiRecordStatus.undefinedError;</span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :       case 'converted':</span></span>
<span id="L41"><span class="lineNum">      41</span>              :         return EmiRecordStatus.converted;</span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :       case 'paying':</span></span>
<span id="L43"><span class="lineNum">      43</span>              :         return EmiRecordStatus.paying;</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :       case 'paid':</span></span>
<span id="L45"><span class="lineNum">      45</span>              :         return EmiRecordStatus.paid;</span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :       case 'converted_manual':</span></span>
<span id="L47"><span class="lineNum">      47</span>              :         return EmiRecordStatus.convertedManual;</span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :       case 'rejected':</span></span>
<span id="L49"><span class="lineNum">      49</span>              :         return EmiRecordStatus.rejected;</span>
<span id="L50"><span class="lineNum">      50</span>              :       default:</span>
<span id="L51"><span class="lineNum">      51</span>              :         return EmiRecordStatus.unknown;</span>
<span id="L52"><span class="lineNum">      52</span>              :     }</span>
<span id="L53"><span class="lineNum">      53</span>              :   }</span>
<span id="L54"><span class="lineNum">      54</span>              : }</span>
<span id="L55"><span class="lineNum">      55</span>              : </span>
<span id="L56"><span class="lineNum">      56</span>              : class EmiRecordEntity extends BaseEntity {</span>
<span id="L57"><span class="lineNum">      57</span>              :   final String? id;</span>
<span id="L58"><span class="lineNum">      58</span>              :   final String? transactionId;</span>
<span id="L59"><span class="lineNum">      59</span>              :   final int? userId;</span>
<span id="L60"><span class="lineNum">      60</span>              :   final EmiRecordStatus? status;</span>
<span id="L61"><span class="lineNum">      61</span>              :   final OrderInfoEntity? orderInfo;</span>
<span id="L62"><span class="lineNum">      62</span>              :   final int? userChargeAmount;</span>
<span id="L63"><span class="lineNum">      63</span>              :   final EmiContactEntity? emiContact;</span>
<span id="L64"><span class="lineNum">      64</span>              :   final EmiPackageEntity? emiPackage;</span>
<span id="L65"><span class="lineNum">      65</span>              :   final String? createdAt;</span>
<span id="L66"><span class="lineNum">      66</span>              :   final MerchantInfoEntity? merchant;</span>
<span id="L67"><span class="lineNum">      67</span>              :   final EmiConversionStatusInfoEntity? conversionStatusInfo;</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :   DateTime? get createdAtDateTime =&gt; commonUtilFunction.toDateTime(createdAt);</span></span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :   String? get createdAtHeader =&gt; commonUtilFunction.toDateTime(createdAt)?.month.toString();</span></span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :   EmiRecordEntity({</span></span>
<span id="L74"><span class="lineNum">      74</span>              :     this.id,</span>
<span id="L75"><span class="lineNum">      75</span>              :     this.transactionId,</span>
<span id="L76"><span class="lineNum">      76</span>              :     this.userId,</span>
<span id="L77"><span class="lineNum">      77</span>              :     this.status,</span>
<span id="L78"><span class="lineNum">      78</span>              :     this.orderInfo,</span>
<span id="L79"><span class="lineNum">      79</span>              :     this.userChargeAmount,</span>
<span id="L80"><span class="lineNum">      80</span>              :     this.emiContact,</span>
<span id="L81"><span class="lineNum">      81</span>              :     this.emiPackage,</span>
<span id="L82"><span class="lineNum">      82</span>              :     this.createdAt,</span>
<span id="L83"><span class="lineNum">      83</span>              :     this.merchant,</span>
<span id="L84"><span class="lineNum">      84</span>              :     this.conversionStatusInfo,</span>
<span id="L85"><span class="lineNum">      85</span>              :   });</span>
<span id="L86"><span class="lineNum">      86</span>              : </span>
<span id="L87"><span class="lineNum">      87</span> <span class="tlaUNC">           0 :   EmiRecordEntity.unserializable()</span></span>
<span id="L88"><span class="lineNum">      88</span>              :       : id = null,</span>
<span id="L89"><span class="lineNum">      89</span>              :         transactionId = null,</span>
<span id="L90"><span class="lineNum">      90</span>              :         userId = null,</span>
<span id="L91"><span class="lineNum">      91</span>              :         status = null,</span>
<span id="L92"><span class="lineNum">      92</span>              :         orderInfo = null,</span>
<span id="L93"><span class="lineNum">      93</span>              :         userChargeAmount = null,</span>
<span id="L94"><span class="lineNum">      94</span>              :         emiContact = null,</span>
<span id="L95"><span class="lineNum">      95</span>              :         emiPackage = null,</span>
<span id="L96"><span class="lineNum">      96</span>              :         createdAt = null,</span>
<span id="L97"><span class="lineNum">      97</span>              :         merchant = null,</span>
<span id="L98"><span class="lineNum">      98</span>              :         conversionStatusInfo = null,</span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :         super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L100"><span class="lineNum">     100</span>              : </span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :   EmiRecordEntity.fromBaseResponse(BaseResponse super.response)</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :       : id = response.data?['id'] as String?,</span></span>
<span id="L103"><span class="lineNum">     103</span> <span class="tlaUNC">           0 :         transactionId = response.data?['transaction_id'] as String?,</span></span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :         userId = response.data?['user_id'] as int?,</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :         status = EmiRecordStatus.fromValue(response.data?['status'] as String?),</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :         orderInfo = (response.data?['order'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :             ? OrderInfoEntity.fromJson(response.data?['order'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L108"><span class="lineNum">     108</span>              :             : null,</span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :         userChargeAmount = response.data?['user_charge_amount'] as int?,</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :         emiContact = (response.data?['emi_contract'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :             ? EmiContactEntity.fromJson(response.data?['emi_contract'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L112"><span class="lineNum">     112</span>              :             : null,</span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :         emiPackage = (response.data?['package'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :             ? EmiPackageEntity.fromJson(response.data?['package'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L115"><span class="lineNum">     115</span>              :             : null,</span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :         createdAt = response.data?['created_at'] as String?,</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :         merchant = (response.data?['merchant'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :             ? MerchantInfoEntity.fromJson(response.data?['merchant'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L119"><span class="lineNum">     119</span>              :             : null,</span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :         conversionStatusInfo = response.data?['conversion_status_info'] != null</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :             ? EmiConversionStatusInfoEntity.fromJson(response.data?['conversion_status_info'])</span></span>
<span id="L122"><span class="lineNum">     122</span>              :             : null,</span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :         super.fromBaseResponse();</span></span>
<span id="L124"><span class="lineNum">     124</span>              : </span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :   EmiRecordEntity.fromJson(Map&lt;String, dynamic&gt; json)</span></span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :       : id = json['id'] as String?,</span></span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :         transactionId = json['transaction_id'] as String?,</span></span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :         userId = json['user_id'] as int?,</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :         status = EmiRecordStatus.fromValue(json['status'] as String?),</span></span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :         orderInfo = (json['order'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :             ? OrderInfoEntity.fromJson(json['order'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L132"><span class="lineNum">     132</span>              :             : null,</span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :         userChargeAmount = json['user_charge_amount'] as int?,</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :         emiContact = (json['emi_contract'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :             ? EmiContactEntity.fromJson(json['emi_contract'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L136"><span class="lineNum">     136</span>              :             : null,</span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaUNC">           0 :         emiPackage = (json['package'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :             ? EmiPackageEntity.fromJson(json['package'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L139"><span class="lineNum">     139</span>              :             : null,</span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaUNC">           0 :         createdAt = json['created_at'] as String?,</span></span>
<span id="L141"><span class="lineNum">     141</span> <span class="tlaUNC">           0 :         merchant = (json['merchant'] as Map&lt;String, dynamic&gt;?) != null</span></span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :             ? MerchantInfoEntity.fromJson(json['merchant'] as Map&lt;String, dynamic&gt;)</span></span>
<span id="L143"><span class="lineNum">     143</span>              :             : null,</span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaUNC">           0 :         conversionStatusInfo = json['conversion_status_info'] != null</span></span>
<span id="L145"><span class="lineNum">     145</span> <span class="tlaUNC">           0 :             ? EmiConversionStatusInfoEntity.fromJson(json['conversion_status_info'])</span></span>
<span id="L146"><span class="lineNum">     146</span>              :             : null;</span>
<span id="L147"><span class="lineNum">     147</span>              : </span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; &lt;String, dynamic&gt;{</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :         'id': id,</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :         'transaction_id': transactionId,</span></span>
<span id="L152"><span class="lineNum">     152</span> <span class="tlaUNC">           0 :         'user_id': userId,</span></span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :         'status': status?.value,</span></span>
<span id="L154"><span class="lineNum">     154</span> <span class="tlaUNC">           0 :         'order': orderInfo?.toJson(),</span></span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaUNC">           0 :         'user_charge_amount': userChargeAmount,</span></span>
<span id="L156"><span class="lineNum">     156</span> <span class="tlaUNC">           0 :         'emi_contract': emiContact?.toJson(),</span></span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :         'package': emiPackage?.toJson(),</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :         'created_at': createdAt,</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :         'merchant': merchant?.toJson(),</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :         'conversion_status_info': conversionStatusInfo?.toJson(),</span></span>
<span id="L161"><span class="lineNum">     161</span>              :       };</span>
<span id="L162"><span class="lineNum">     162</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
