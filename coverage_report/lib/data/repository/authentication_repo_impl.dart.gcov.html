<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/repository/authentication_repo_impl.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/repository">lib/data/repository</a> - authentication_repo_impl.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">126</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/http_client/common_request_option.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../feature/feature_toggle.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../util/functions.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../util/secure_storage_helper/secure_storage_helper.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../constants.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../request/face_auth_request.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../response/reset_pin_entity.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../response/sign_in_entity.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../response/sign_in_otp_entity.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import 'authentication_repo.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import 'base_repo.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span>              : class AuthenticationRepoImpl extends BaseRepo implements AuthenticationRepo {</span>
<span id="L20"><span class="lineNum">      20</span>              :   final CommonHttpClient nonAuthenticationEvoHttpClient;</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaUNC">           0 :   AuthenticationRepoImpl({</span></span>
<span id="L23"><span class="lineNum">      23</span>              :     required CommonHttpClient evoHttpClient,</span>
<span id="L24"><span class="lineNum">      24</span>              :     required EvoLocalStorageHelper evoLocalStorageHelper,</span>
<span id="L25"><span class="lineNum">      25</span>              :     required this.nonAuthenticationEvoHttpClient,</span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaUNC">           0 :   }) : super(</span></span>
<span id="L27"><span class="lineNum">      27</span>              :           evoHttpClient,</span>
<span id="L28"><span class="lineNum">      28</span>              :           localStorageHelper: evoLocalStorageHelper,</span>
<span id="L29"><span class="lineNum">      29</span>              :         );</span>
<span id="L30"><span class="lineNum">      30</span>              : </span>
<span id="L31"><span class="lineNum">      31</span>              :   // API urls</span>
<span id="L32"><span class="lineNum">      32</span>              :   static const String signInUrl = 'user/signin';</span>
<span id="L33"><span class="lineNum">      33</span>              :   static const String logoutUrl = 'user/signout';</span>
<span id="L34"><span class="lineNum">      34</span>              :   static const String resetPin = 'user/pin/reset';</span>
<span id="L35"><span class="lineNum">      35</span>              :   static const String changePin = 'user/pin/confirm-reset';</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span>              :   // Refer API SPEC: https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/SignIn/SigninDOP</span>
<span id="L38"><span class="lineNum">      38</span>              :   static const String signInFromDOPNativeUrl = 'user/dop/signin';</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span>              :   // API url v1 for Non-Face OTP</span>
<span id="L41"><span class="lineNum">      41</span>              :   static const String verifySignInUrlV1 = '$signInUrl/verify';</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span>              :   // API url v2 for Face OTP</span>
<span id="L44"><span class="lineNum">      44</span>              :   static const String verifySignInUrlV2 = 'v2/$signInUrl/verify';</span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span>              :   // API url v2 for Face Auth</span>
<span id="L47"><span class="lineNum">      47</span>              :   static const String verifySignInUrlV3 = 'v3/$signInUrl/verify';</span>
<span id="L48"><span class="lineNum">      48</span>              : </span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L50"><span class="lineNum">      50</span>              :   Future&lt;BaseEntity?&gt; logout({MockConfig? mockConfig}) async {</span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(logoutUrl, mockConfig: mockConfig);</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :     final BaseEntity baseEntity = commonUtilFunction.serialize(</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :           () =&gt; BaseEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L54"><span class="lineNum">      54</span>              :           originalData: baseResponse,</span>
<span id="L55"><span class="lineNum">      55</span>              :         ) ??</span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :         BaseEntity(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L57"><span class="lineNum">      57</span>              :     return baseEntity;</span>
<span id="L58"><span class="lineNum">      58</span>              :   }</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L61"><span class="lineNum">      61</span>              :   Future&lt;SignInEntity&gt; verifyPhone(</span>
<span id="L62"><span class="lineNum">      62</span>              :       {required String phoneNumber, required TypeLogin type, MockConfig? mockConfig}) async {</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(signInUrl,</span></span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :         data: &lt;String, dynamic&gt;{</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :           SignInRequestApiType.phoneNumber.value: phoneNumber,</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :           SignInRequestApiType.type.value: type.value</span></span>
<span id="L67"><span class="lineNum">      67</span>              :         },</span>
<span id="L68"><span class="lineNum">      68</span>              :         mockConfig: mockConfig);</span>
<span id="L69"><span class="lineNum">      69</span>              : </span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :     final SignInEntity signInEntity = commonUtilFunction.serialize(</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :             () =&gt; SignInEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L72"><span class="lineNum">      72</span>              :             originalData: baseResponse) ??</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :         SignInEntity.unserializable();</span></span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span>              :     return signInEntity;</span>
<span id="L76"><span class="lineNum">      76</span>              :   }</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L79"><span class="lineNum">      79</span>              :   Future&lt;SignInOtpEntity&gt; login(</span>
<span id="L80"><span class="lineNum">      80</span>              :     TypeLogin type, {</span>
<span id="L81"><span class="lineNum">      81</span>              :     FacialVerificationVersion facialVerificationVersion = FacialVerificationVersion.version_3,</span>
<span id="L82"><span class="lineNum">      82</span>              :     String? otp,</span>
<span id="L83"><span class="lineNum">      83</span>              :     String? pin,</span>
<span id="L84"><span class="lineNum">      84</span>              :     String? token,</span>
<span id="L85"><span class="lineNum">      85</span>              :     String? phoneNumber,</span>
<span id="L86"><span class="lineNum">      86</span>              :     String? selfieImageId,</span>
<span id="L87"><span class="lineNum">      87</span>              :     FaceAuthRequest? faceAuthRequest,</span>
<span id="L88"><span class="lineNum">      88</span>              :     String? sessionToken,</span>
<span id="L89"><span class="lineNum">      89</span>              :     MockConfig? mockConfig,</span>
<span id="L90"><span class="lineNum">      90</span>              :   }) async {</span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; dataMap = &lt;String, dynamic&gt;{</span></span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :       SignInRequestApiType.type.value: type.value,</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :       if (phoneNumber != null) SignInRequestApiType.phoneNumber.value: phoneNumber</span></span>
<span id="L94"><span class="lineNum">      94</span>              :     };</span>
<span id="L95"><span class="lineNum">      95</span> <span class="tlaUNC">           0 :     if (type == TypeLogin.otp) {</span></span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :       dataMap['otp'] = otp;</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :     } else if (type == TypeLogin.token) {</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :       dataMap['token'] = token;</span></span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :     } else if (type == TypeLogin.faceOTP) {</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :       dataMap['selfie_image_id'] = selfieImageId;</span></span>
<span id="L101"><span class="lineNum">     101</span> <span class="tlaUNC">           0 :     } else if (type == TypeLogin.faceAuth) {</span></span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :       dataMap['face_auth_request'] = faceAuthRequest?.toJson();</span></span>
<span id="L103"><span class="lineNum">     103</span>              :     } else {</span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :       dataMap['pin'] = pin;</span></span>
<span id="L105"><span class="lineNum">     105</span>              :     }</span>
<span id="L106"><span class="lineNum">     106</span>              : </span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; headers = &lt;String, dynamic&gt;{};</span></span>
<span id="L108"><span class="lineNum">     108</span>              : </span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :     final String? deviceToken = await localStorageHelper?.getDeviceToken();</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :     if (deviceToken?.isNotEmpty == true) {</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :       headers.addAll(_getDeviceTokenHeaders(deviceToken));</span></span>
<span id="L112"><span class="lineNum">     112</span>              :     }</span>
<span id="L113"><span class="lineNum">     113</span>              : </span>
<span id="L114"><span class="lineNum">     114</span> <span class="tlaUNC">           0 :     if (sessionToken?.isNotEmpty == true) {</span></span>
<span id="L115"><span class="lineNum">     115</span>              :       /// Store the sessionToken on memory to send to server as a header in further api in Authentication flow</span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :       headers.addAll(_getSessionTokenHeaders(sessionToken));</span></span>
<span id="L117"><span class="lineNum">     117</span>              :     }</span>
<span id="L118"><span class="lineNum">     118</span>              : </span>
<span id="L119"><span class="lineNum">     119</span>              :     /// this API handle 2 logic: face is live or not &amp; face matching.</span>
<span id="L120"><span class="lineNum">     120</span>              :     /// in some case we need to wait for 30s to get result</span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :     final Duration? receiveTimeout = type == TypeLogin.faceAuth || type == TypeLogin.faceOTP</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :         ? Duration(seconds: DurationConstants.facialVerificationTimeoutInSeconds)</span></span>
<span id="L123"><span class="lineNum">     123</span>              :         : null;</span>
<span id="L124"><span class="lineNum">     124</span>              : </span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L126"><span class="lineNum">     126</span>              :       switch (facialVerificationVersion) {</span>
<span id="L127"><span class="lineNum">     127</span> <span class="tlaUNC">           0 :         FacialVerificationVersion.version_1 =&gt; verifySignInUrlV1,</span></span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :         FacialVerificationVersion.version_2 =&gt; verifySignInUrlV2,</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :         FacialVerificationVersion.version_3 =&gt; verifySignInUrlV3,</span></span>
<span id="L130"><span class="lineNum">     130</span>              :       },</span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :       requestOption: CommonRequestOption(</span></span>
<span id="L132"><span class="lineNum">     132</span>              :         headers: headers,</span>
<span id="L133"><span class="lineNum">     133</span>              :         receiveTimeout: receiveTimeout,</span>
<span id="L134"><span class="lineNum">     134</span>              :       ),</span>
<span id="L135"><span class="lineNum">     135</span>              :       data: dataMap,</span>
<span id="L136"><span class="lineNum">     136</span>              :       mockConfig: mockConfig,</span>
<span id="L137"><span class="lineNum">     137</span>              :     );</span>
<span id="L138"><span class="lineNum">     138</span>              : </span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :     final SignInOtpEntity signInOtpEntity = commonUtilFunction.serialize(</span></span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaUNC">           0 :             () =&gt; SignInOtpEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L141"><span class="lineNum">     141</span>              :             originalData: baseResponse) ??</span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :         SignInOtpEntity.unserializable();</span></span>
<span id="L143"><span class="lineNum">     143</span>              : </span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaUNC">           0 :     if (signInOtpEntity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L145"><span class="lineNum">     145</span> <span class="tlaUNC">           0 :       await _handleSignInSucceedData(</span></span>
<span id="L146"><span class="lineNum">     146</span> <span class="tlaUNC">           0 :         accessToken: signInOtpEntity.accessToken,</span></span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :         refreshToken: signInOtpEntity.refreshToken,</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :         biometricToken: signInOtpEntity.biometricToken,</span></span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :         deviceToken: signInOtpEntity.deviceToken,</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :         userId: signInOtpEntity.userId,</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :         notificationAuthKey: signInOtpEntity.notificationAuthKey,</span></span>
<span id="L152"><span class="lineNum">     152</span>              :       );</span>
<span id="L153"><span class="lineNum">     153</span>              :     }</span>
<span id="L154"><span class="lineNum">     154</span>              :     return signInOtpEntity;</span>
<span id="L155"><span class="lineNum">     155</span>              :   }</span>
<span id="L156"><span class="lineNum">     156</span>              : </span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :   Map&lt;String, String?&gt; _getSessionTokenHeaders(String? sessionToken) {</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :     return &lt;String, String?&gt;{HeaderKey.sessionToken: sessionToken};</span></span>
<span id="L159"><span class="lineNum">     159</span>              :   }</span>
<span id="L160"><span class="lineNum">     160</span>              : </span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :   Map&lt;String, String?&gt; _getDeviceTokenHeaders(String? deviceToken) {</span></span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaUNC">           0 :     return &lt;String, String?&gt;{HeaderKey.deviceToken: deviceToken};</span></span>
<span id="L163"><span class="lineNum">     163</span>              :   }</span>
<span id="L164"><span class="lineNum">     164</span>              : </span>
<span id="L165"><span class="lineNum">     165</span>              :   /// This function mostly like [login] function.</span>
<span id="L166"><span class="lineNum">     166</span>              :   /// The different is [refreshToken]  function will use [nonAuthenticationEvoHttpClient] to make api call, instead of [client]</span>
<span id="L167"><span class="lineNum">     167</span>              :   /// We accept to duplicate some code but avoid confuse between [nonAuthenticationEvoHttpClient] and [client] if using the same [login] function</span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L169"><span class="lineNum">     169</span>              :   Future&lt;SignInOtpEntity&gt; refreshToken(String? token) async {</span>
<span id="L170"><span class="lineNum">     170</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; dataMap = &lt;String, dynamic&gt;{</span></span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaUNC">           0 :       'type': TypeLogin.token.value,</span></span>
<span id="L172"><span class="lineNum">     172</span>              :       'token': token,</span>
<span id="L173"><span class="lineNum">     173</span>              :     };</span>
<span id="L174"><span class="lineNum">     174</span>              : </span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; headers = &lt;String, dynamic&gt;{};</span></span>
<span id="L176"><span class="lineNum">     176</span>              : </span>
<span id="L177"><span class="lineNum">     177</span> <span class="tlaUNC">           0 :     final String? deviceToken = await localStorageHelper?.getDeviceToken();</span></span>
<span id="L178"><span class="lineNum">     178</span> <span class="tlaUNC">           0 :     if (deviceToken?.isNotEmpty == true) {</span></span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :       headers.addAll(_getDeviceTokenHeaders(deviceToken));</span></span>
<span id="L180"><span class="lineNum">     180</span>              :     }</span>
<span id="L181"><span class="lineNum">     181</span>              : </span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await nonAuthenticationEvoHttpClient.post(</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :       switch (evoUtilFunction.getFacialVerificationVersion()) {</span></span>
<span id="L184"><span class="lineNum">     184</span> <span class="tlaUNC">           0 :         FacialVerificationVersion.version_1 =&gt; verifySignInUrlV1,</span></span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :         FacialVerificationVersion.version_2 =&gt; verifySignInUrlV2,</span></span>
<span id="L186"><span class="lineNum">     186</span> <span class="tlaUNC">           0 :         FacialVerificationVersion.version_3 =&gt; verifySignInUrlV3,</span></span>
<span id="L187"><span class="lineNum">     187</span>              :       },</span>
<span id="L188"><span class="lineNum">     188</span> <span class="tlaUNC">           0 :       requestOption: CommonRequestOption(headers: headers),</span></span>
<span id="L189"><span class="lineNum">     189</span>              :       data: dataMap,</span>
<span id="L190"><span class="lineNum">     190</span>              :     );</span>
<span id="L191"><span class="lineNum">     191</span>              : </span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaUNC">           0 :     final SignInOtpEntity signInOtpEntity = commonUtilFunction.serialize(</span></span>
<span id="L193"><span class="lineNum">     193</span> <span class="tlaUNC">           0 :             () =&gt; SignInOtpEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L194"><span class="lineNum">     194</span>              :             originalData: baseResponse) ??</span>
<span id="L195"><span class="lineNum">     195</span> <span class="tlaUNC">           0 :         SignInOtpEntity.unserializable();</span></span>
<span id="L196"><span class="lineNum">     196</span>              : </span>
<span id="L197"><span class="lineNum">     197</span> <span class="tlaUNC">           0 :     if (signInOtpEntity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaUNC">           0 :       await _handleSignInSucceedData(</span></span>
<span id="L199"><span class="lineNum">     199</span> <span class="tlaUNC">           0 :         accessToken: signInOtpEntity.accessToken,</span></span>
<span id="L200"><span class="lineNum">     200</span> <span class="tlaUNC">           0 :         refreshToken: signInOtpEntity.refreshToken,</span></span>
<span id="L201"><span class="lineNum">     201</span> <span class="tlaUNC">           0 :         deviceToken: signInOtpEntity.deviceToken,</span></span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaUNC">           0 :         userId: signInOtpEntity.userId,</span></span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :         notificationAuthKey: signInOtpEntity.notificationAuthKey,</span></span>
<span id="L204"><span class="lineNum">     204</span>              :       );</span>
<span id="L205"><span class="lineNum">     205</span>              :     }</span>
<span id="L206"><span class="lineNum">     206</span>              : </span>
<span id="L207"><span class="lineNum">     207</span>              :     return signInOtpEntity;</span>
<span id="L208"><span class="lineNum">     208</span>              :   }</span>
<span id="L209"><span class="lineNum">     209</span>              : </span>
<span id="L210"><span class="lineNum">     210</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L211"><span class="lineNum">     211</span>              :   Future&lt;ResetPinEntity&gt; requestResetPin(</span>
<span id="L212"><span class="lineNum">     212</span>              :     ResetPinType type, {</span>
<span id="L213"><span class="lineNum">     213</span>              :     String? phoneNumber,</span>
<span id="L214"><span class="lineNum">     214</span>              :     String? nationalId,</span>
<span id="L215"><span class="lineNum">     215</span>              :     String? otp,</span>
<span id="L216"><span class="lineNum">     216</span>              :     String? pin,</span>
<span id="L217"><span class="lineNum">     217</span>              :     String? sessionToken,</span>
<span id="L218"><span class="lineNum">     218</span>              :     MockConfig? mockConfig,</span>
<span id="L219"><span class="lineNum">     219</span>              :   }) async {</span>
<span id="L220"><span class="lineNum">     220</span>              :     String urlApi = resetPin;</span>
<span id="L221"><span class="lineNum">     221</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; dataMap = &lt;String, dynamic&gt;{'type': type.value};</span></span>
<span id="L222"><span class="lineNum">     222</span>              :     switch (type) {</span>
<span id="L223"><span class="lineNum">     223</span> <span class="tlaUNC">           0 :       case ResetPinType.verifyNationalId:</span></span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaUNC">           0 :         dataMap['national_id'] = nationalId;</span></span>
<span id="L225"><span class="lineNum">     225</span>              :         break;</span>
<span id="L226"><span class="lineNum">     226</span> <span class="tlaUNC">           0 :       case ResetPinType.verifyOtp:</span></span>
<span id="L227"><span class="lineNum">     227</span> <span class="tlaUNC">           0 :         dataMap['otp'] = otp;</span></span>
<span id="L228"><span class="lineNum">     228</span>              :         break;</span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaUNC">           0 :       case ResetPinType.changePin:</span></span>
<span id="L230"><span class="lineNum">     230</span> <span class="tlaUNC">           0 :         dataMap['phone_number'] = phoneNumber;</span></span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaUNC">           0 :         dataMap['pin'] = pin;</span></span>
<span id="L232"><span class="lineNum">     232</span>              :         urlApi = changePin;</span>
<span id="L233"><span class="lineNum">     233</span>              :         break;</span>
<span id="L234"><span class="lineNum">     234</span> <span class="tlaUNC">           0 :       case ResetPinType.resendOtp:</span></span>
<span id="L235"><span class="lineNum">     235</span> <span class="tlaUNC">           0 :         dataMap['phone_number'] = phoneNumber;</span></span>
<span id="L236"><span class="lineNum">     236</span>              :         break;</span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaUNC">           0 :       case ResetPinType.none:</span></span>
<span id="L238"><span class="lineNum">     238</span> <span class="tlaUNC">           0 :         dataMap['phone_number'] = phoneNumber;</span></span>
<span id="L239"><span class="lineNum">     239</span>              :         break;</span>
<span id="L240"><span class="lineNum">     240</span>              :       default:</span>
<span id="L241"><span class="lineNum">     241</span>              :     }</span>
<span id="L242"><span class="lineNum">     242</span>              : </span>
<span id="L243"><span class="lineNum">     243</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; headers = &lt;String, dynamic&gt;{};</span></span>
<span id="L244"><span class="lineNum">     244</span> <span class="tlaUNC">           0 :     if (sessionToken?.isNotEmpty == true) {</span></span>
<span id="L245"><span class="lineNum">     245</span> <span class="tlaUNC">           0 :       headers.addAll(_getSessionTokenHeaders(sessionToken));</span></span>
<span id="L246"><span class="lineNum">     246</span>              :     }</span>
<span id="L247"><span class="lineNum">     247</span>              : </span>
<span id="L248"><span class="lineNum">     248</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.patch(</span></span>
<span id="L249"><span class="lineNum">     249</span>              :       urlApi,</span>
<span id="L250"><span class="lineNum">     250</span> <span class="tlaUNC">           0 :       requestOption: CommonRequestOption(headers: headers),</span></span>
<span id="L251"><span class="lineNum">     251</span>              :       data: dataMap,</span>
<span id="L252"><span class="lineNum">     252</span>              :       mockConfig: mockConfig,</span>
<span id="L253"><span class="lineNum">     253</span>              :     );</span>
<span id="L254"><span class="lineNum">     254</span>              : </span>
<span id="L255"><span class="lineNum">     255</span> <span class="tlaUNC">           0 :     final ResetPinEntity resetPinEntity = commonUtilFunction.serialize(</span></span>
<span id="L256"><span class="lineNum">     256</span> <span class="tlaUNC">           0 :             () =&gt; ResetPinEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L257"><span class="lineNum">     257</span>              :             originalData: baseResponse) ??</span>
<span id="L258"><span class="lineNum">     258</span> <span class="tlaUNC">           0 :         ResetPinEntity.unSerializable();</span></span>
<span id="L259"><span class="lineNum">     259</span>              : </span>
<span id="L260"><span class="lineNum">     260</span>              :     return resetPinEntity;</span>
<span id="L261"><span class="lineNum">     261</span>              :   }</span>
<span id="L262"><span class="lineNum">     262</span>              : </span>
<span id="L263"><span class="lineNum">     263</span> <span class="tlaUNC">           0 :   Future&lt;void&gt; _handleSignInSucceedData({</span></span>
<span id="L264"><span class="lineNum">     264</span>              :     required String? accessToken,</span>
<span id="L265"><span class="lineNum">     265</span>              :     required String? refreshToken,</span>
<span id="L266"><span class="lineNum">     266</span>              :     required String? deviceToken,</span>
<span id="L267"><span class="lineNum">     267</span>              :     String? biometricToken,</span>
<span id="L268"><span class="lineNum">     268</span>              :     int? userId,</span>
<span id="L269"><span class="lineNum">     269</span>              :     String? notificationAuthKey,</span>
<span id="L270"><span class="lineNum">     270</span>              :   }) async {</span>
<span id="L271"><span class="lineNum">     271</span> <span class="tlaUNC">           0 :     return saveAuthenticationInfo(</span></span>
<span id="L272"><span class="lineNum">     272</span>              :         accessToken: accessToken,</span>
<span id="L273"><span class="lineNum">     273</span>              :         refreshToken: refreshToken,</span>
<span id="L274"><span class="lineNum">     274</span>              :         biometricToken: biometricToken,</span>
<span id="L275"><span class="lineNum">     275</span>              :         deviceToken: deviceToken,</span>
<span id="L276"><span class="lineNum">     276</span>              :         userId: userId,</span>
<span id="L277"><span class="lineNum">     277</span>              :         notificationAuthKey: notificationAuthKey);</span>
<span id="L278"><span class="lineNum">     278</span>              :   }</span>
<span id="L279"><span class="lineNum">     279</span>              : </span>
<span id="L280"><span class="lineNum">     280</span>              :   /// Note: this API is not require authentication</span>
<span id="L281"><span class="lineNum">     281</span>              :   ///</span>
<span id="L282"><span class="lineNum">     282</span>              :   /// Using [nonAuthenticationEvoHttpClient] to avoid the [401] error code handled by [UnAuthorizedInterceptor]</span>
<span id="L283"><span class="lineNum">     283</span>              :   /// And avoid redundant Authorization header in the request</span>
<span id="L284"><span class="lineNum">     284</span>              :   /// This API not has the access_token of the EVO app in authentication header,</span>
<span id="L285"><span class="lineNum">     285</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L286"><span class="lineNum">     286</span>              :   Future&lt;SignInOtpEntity&gt; loginFromDOE({</span>
<span id="L287"><span class="lineNum">     287</span>              :     TypeLogin? type,</span>
<span id="L288"><span class="lineNum">     288</span>              :     String? pin,</span>
<span id="L289"><span class="lineNum">     289</span>              :     String? dopAccessToken,</span>
<span id="L290"><span class="lineNum">     290</span>              :     String? sessionToken,</span>
<span id="L291"><span class="lineNum">     291</span>              :     MockConfig? mockConfig,</span>
<span id="L292"><span class="lineNum">     292</span>              :   }) async {</span>
<span id="L293"><span class="lineNum">     293</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; dataMap = &lt;String, dynamic&gt;{};</span></span>
<span id="L294"><span class="lineNum">     294</span>              : </span>
<span id="L295"><span class="lineNum">     295</span>              :     if (type != null) {</span>
<span id="L296"><span class="lineNum">     296</span> <span class="tlaUNC">           0 :       dataMap[SignInRequestApiType.type.value] = type.value;</span></span>
<span id="L297"><span class="lineNum">     297</span>              :     }</span>
<span id="L298"><span class="lineNum">     298</span>              : </span>
<span id="L299"><span class="lineNum">     299</span>              :     if (pin != null) {</span>
<span id="L300"><span class="lineNum">     300</span> <span class="tlaUNC">           0 :       dataMap['pin'] = pin;</span></span>
<span id="L301"><span class="lineNum">     301</span>              :     }</span>
<span id="L302"><span class="lineNum">     302</span>              : </span>
<span id="L303"><span class="lineNum">     303</span>              :     if (dopAccessToken != null) {</span>
<span id="L304"><span class="lineNum">     304</span> <span class="tlaUNC">           0 :       dataMap['dop_access_token'] = dopAccessToken;</span></span>
<span id="L305"><span class="lineNum">     305</span>              :     }</span>
<span id="L306"><span class="lineNum">     306</span>              : </span>
<span id="L307"><span class="lineNum">     307</span> <span class="tlaUNC">           0 :     final String? deviceToken = await localStorageHelper?.getDeviceToken();</span></span>
<span id="L308"><span class="lineNum">     308</span> <span class="tlaUNC">           0 :     if (deviceToken != null &amp;&amp; type != TypeLogin.pin) {</span></span>
<span id="L309"><span class="lineNum">     309</span> <span class="tlaUNC">           0 :       dataMap['device_token'] = deviceToken;</span></span>
<span id="L310"><span class="lineNum">     310</span>              :     }</span>
<span id="L311"><span class="lineNum">     311</span>              : </span>
<span id="L312"><span class="lineNum">     312</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; headers = &lt;String, dynamic&gt;{};</span></span>
<span id="L313"><span class="lineNum">     313</span> <span class="tlaUNC">           0 :     if (sessionToken?.isNotEmpty == true) {</span></span>
<span id="L314"><span class="lineNum">     314</span> <span class="tlaUNC">           0 :       headers.addAll(_getSessionTokenHeaders(sessionToken));</span></span>
<span id="L315"><span class="lineNum">     315</span>              :     }</span>
<span id="L316"><span class="lineNum">     316</span>              : </span>
<span id="L317"><span class="lineNum">     317</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await nonAuthenticationEvoHttpClient.post(</span></span>
<span id="L318"><span class="lineNum">     318</span>              :       signInFromDOPNativeUrl,</span>
<span id="L319"><span class="lineNum">     319</span>              :       data: dataMap,</span>
<span id="L320"><span class="lineNum">     320</span> <span class="tlaUNC">           0 :       requestOption: CommonRequestOption(headers: headers),</span></span>
<span id="L321"><span class="lineNum">     321</span>              :       mockConfig: mockConfig,</span>
<span id="L322"><span class="lineNum">     322</span>              :     );</span>
<span id="L323"><span class="lineNum">     323</span>              : </span>
<span id="L324"><span class="lineNum">     324</span> <span class="tlaUNC">           0 :     final SignInOtpEntity signInEntity = commonUtilFunction.serialize(</span></span>
<span id="L325"><span class="lineNum">     325</span> <span class="tlaUNC">           0 :             () =&gt; SignInOtpEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L326"><span class="lineNum">     326</span>              :             originalData: baseResponse) ??</span>
<span id="L327"><span class="lineNum">     327</span> <span class="tlaUNC">           0 :         SignInOtpEntity.unserializable();</span></span>
<span id="L328"><span class="lineNum">     328</span>              : </span>
<span id="L329"><span class="lineNum">     329</span> <span class="tlaUNC">           0 :     if (signInEntity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L330"><span class="lineNum">     330</span> <span class="tlaUNC">           0 :       await _handleSignInSucceedData(</span></span>
<span id="L331"><span class="lineNum">     331</span> <span class="tlaUNC">           0 :         accessToken: signInEntity.accessToken,</span></span>
<span id="L332"><span class="lineNum">     332</span> <span class="tlaUNC">           0 :         refreshToken: signInEntity.refreshToken,</span></span>
<span id="L333"><span class="lineNum">     333</span> <span class="tlaUNC">           0 :         biometricToken: signInEntity.biometricToken,</span></span>
<span id="L334"><span class="lineNum">     334</span> <span class="tlaUNC">           0 :         deviceToken: signInEntity.deviceToken,</span></span>
<span id="L335"><span class="lineNum">     335</span> <span class="tlaUNC">           0 :         userId: signInEntity.userId,</span></span>
<span id="L336"><span class="lineNum">     336</span> <span class="tlaUNC">           0 :         notificationAuthKey: signInEntity.authNotiToken,</span></span>
<span id="L337"><span class="lineNum">     337</span>              :       );</span>
<span id="L338"><span class="lineNum">     338</span>              :     }</span>
<span id="L339"><span class="lineNum">     339</span>              :     return signInEntity;</span>
<span id="L340"><span class="lineNum">     340</span>              :   }</span>
<span id="L341"><span class="lineNum">     341</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
