<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/repository/mock_test_config_repo_impl.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/repository">lib/data/repository</a> - mock_test_config_repo_impl.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">26</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : // ignore_for_file: avoid_catches_without_on_clauses</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'dart:convert';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'dart:io';</span>
<span id="L4"><span class="lineNum">       4</span>              : </span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter/foundation.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../feature/mock_test/model/mock_test.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../util/extension.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../util/file_browser/file_browser_helper.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import 'mock_test_config_repo.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : </span>
<span id="L13"><span class="lineNum">      13</span>              : class MockTestConfigRepoImpl extends MockTestConfigRepo {</span>
<span id="L14"><span class="lineNum">      14</span>              :   FileBrowserHelper fileBrowser;</span>
<span id="L15"><span class="lineNum">      15</span>              : </span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaUNC">           0 :   MockTestConfigRepoImpl(this.fileBrowser);</span></span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L19"><span class="lineNum">      19</span>              :   Future&lt;MockTestData?&gt; getMockTestConfigFromLocal({</span>
<span id="L20"><span class="lineNum">      20</span>              :     required String prefixConfigFileName,</span>
<span id="L21"><span class="lineNum">      21</span>              :     required String configFileFolderPath,</span>
<span id="L22"><span class="lineNum">      22</span>              :     required List&lt;String&gt; extensionsConfigFile,</span>
<span id="L23"><span class="lineNum">      23</span>              :   }) async {</span>
<span id="L24"><span class="lineNum">      24</span>              :     try {</span>
<span id="L25"><span class="lineNum">      25</span>              :       /// Get base directory</span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaUNC">           0 :       final Directory dir = await fileBrowser.getEndUserInteractableDir();</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :       commonLog('Directory ${dir.path}');</span></span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              :       /// Get config file path</span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :       final String mockTestConfigFilePath = getDirPath(dir, configFileFolderPath);</span></span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span>              :       /// Get config content</span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :       final Map&lt;String, dynamic&gt;? configContent = await getMockTestConfigData(</span></span>
<span id="L34"><span class="lineNum">      34</span>              :         fileConfigPath: mockTestConfigFilePath,</span>
<span id="L35"><span class="lineNum">      35</span>              :         extensionsConfigFile: extensionsConfigFile,</span>
<span id="L36"><span class="lineNum">      36</span>              :         prefixConfigFileName: prefixConfigFileName,</span>
<span id="L37"><span class="lineNum">      37</span>              :       );</span>
<span id="L38"><span class="lineNum">      38</span>              : </span>
<span id="L39"><span class="lineNum">      39</span>              :       if (configContent == null) {</span>
<span id="L40"><span class="lineNum">      40</span>              :         return null;</span>
<span id="L41"><span class="lineNum">      41</span>              :       }</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span>              :       /// Convert json to object</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :       final MockTestData config = MockTestData.fromJson(configContent);</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :       config.dirPath = mockTestConfigFilePath;</span></span>
<span id="L46"><span class="lineNum">      46</span>              : </span>
<span id="L47"><span class="lineNum">      47</span>              :       return config;</span>
<span id="L48"><span class="lineNum">      48</span>              :     } catch (e) {</span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :       commonLog('Read automation test config error: $e');</span></span>
<span id="L50"><span class="lineNum">      50</span>              :     }</span>
<span id="L51"><span class="lineNum">      51</span>              : </span>
<span id="L52"><span class="lineNum">      52</span>              :     return null;</span>
<span id="L53"><span class="lineNum">      53</span>              :   }</span>
<span id="L54"><span class="lineNum">      54</span>              : </span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L56"><span class="lineNum">      56</span>              :   String getDirPath(Directory baseFolder, String configFileFolderPath) {</span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :     final String path = '${baseFolder.path}/$configFileFolderPath';</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :     return removeConsecutiveSlashes(path);</span></span>
<span id="L59"><span class="lineNum">      59</span>              :   }</span>
<span id="L60"><span class="lineNum">      60</span>              : </span>
<span id="L61"><span class="lineNum">      61</span>              :   /// Replace all instances of one or more consecutive slashes with a single slash</span>
<span id="L62"><span class="lineNum">      62</span>              :   /// e.g: 'random/string/with///consecutive/slashes' -&gt; 'random/string/with/consecutive/slashes'</span>
<span id="L63"><span class="lineNum">      63</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L64"><span class="lineNum">      64</span>              :   String removeConsecutiveSlashes(String str) {</span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :     return str.replaceAll(RegExp(r'/+'), '/');</span></span>
<span id="L66"><span class="lineNum">      66</span>              :   }</span>
<span id="L67"><span class="lineNum">      67</span>              : </span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :   Future&lt;Map&lt;String, dynamic&gt;?&gt; getMockTestConfigData({</span></span>
<span id="L69"><span class="lineNum">      69</span>              :     required String fileConfigPath,</span>
<span id="L70"><span class="lineNum">      70</span>              :     required List&lt;String&gt; extensionsConfigFile,</span>
<span id="L71"><span class="lineNum">      71</span>              :     required String prefixConfigFileName,</span>
<span id="L72"><span class="lineNum">      72</span>              :   }) async {</span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :     final List&lt;FileSystemEntity&gt; data = await fileBrowser.getAllFilesInDir(</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :       Directory(fileConfigPath),</span></span>
<span id="L75"><span class="lineNum">      75</span>              :       extensions: extensionsConfigFile,</span>
<span id="L76"><span class="lineNum">      76</span>              :     );</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :     final String? fileContent = await getFileContent(data, prefixConfigFileName);</span></span>
<span id="L79"><span class="lineNum">      79</span>              : </span>
<span id="L80"><span class="lineNum">      80</span>              :     if (fileContent == null) {</span>
<span id="L81"><span class="lineNum">      81</span>              :       return null;</span>
<span id="L82"><span class="lineNum">      82</span>              :     }</span>
<span id="L83"><span class="lineNum">      83</span>              : </span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; jsonContent = convertJsonStringToMap(fileContent);</span></span>
<span id="L85"><span class="lineNum">      85</span> <span class="tlaUNC">           0 :     commonLog('jsonContent: $jsonContent');</span></span>
<span id="L86"><span class="lineNum">      86</span>              :     return jsonContent;</span>
<span id="L87"><span class="lineNum">      87</span>              :   }</span>
<span id="L88"><span class="lineNum">      88</span>              : </span>
<span id="L89"><span class="lineNum">      89</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L90"><span class="lineNum">      90</span>              :   Map&lt;String, dynamic&gt; convertJsonStringToMap(String fileContent) =&gt;</span>
<span id="L91"><span class="lineNum">      91</span> <span class="tlaUNC">           0 :       json.decode(fileContent) as Map&lt;String, dynamic&gt;;</span></span>
<span id="L92"><span class="lineNum">      92</span>              : </span>
<span id="L93"><span class="lineNum">      93</span>              :   /// Get the content of the file that has the prefix name [prefixConfigFileName]</span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :   @visibleForTesting</span></span>
<span id="L95"><span class="lineNum">      95</span>              :   Future&lt;String?&gt; getFileContent(List&lt;FileSystemEntity&gt; data, String prefixConfigFileName) async {</span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :     for (final FileSystemEntity file in data) {</span></span>
<span id="L97"><span class="lineNum">      97</span> <span class="tlaUNC">           0 :       if (file.fileName.startsWith(prefixConfigFileName)) {</span></span>
<span id="L98"><span class="lineNum">      98</span> <span class="tlaUNC">           0 :         return await fileBrowser.readContentFile(file.path);</span></span>
<span id="L99"><span class="lineNum">      99</span>              :       }</span>
<span id="L100"><span class="lineNum">     100</span>              :     }</span>
<span id="L101"><span class="lineNum">     101</span>              :     return null;</span>
<span id="L102"><span class="lineNum">     102</span>              :   }</span>
<span id="L103"><span class="lineNum">     103</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
