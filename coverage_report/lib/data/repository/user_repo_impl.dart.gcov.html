<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/repository/user_repo_impl.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/repository">lib/data/repository</a> - user_repo_impl.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">126</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_http_client.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/http_client/common_request_option.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../feature/biometric/base/ext_biometric_token_entity.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../feature/feature_toggle.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../util/secure_storage_helper/secure_storage_helper.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../constants.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../response/biometric_token_entity.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../response/card_activate_entity.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../response/card_activation_status_entity.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../response/card_status_entity.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../response/link_card_submission_status_entity.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../response/linked_card_list_entity.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../response/linked_card_status_checking_entity.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../response/payment_method_list_entity.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../response/setup_pos_limit_entity.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../response/sign_in_otp_entity.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../response/submit_link_card_entity.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../response/user_deletion_verification_entity.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../response/user_entity.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import 'authentication_repo.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import 'base_repo.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : import 'user_repo.dart';</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              : class UserRepoImpl extends BaseRepo implements UserRepo {</span>
<span id="L30"><span class="lineNum">      30</span>              :   static const String createPinUrl = 'user/pin/create';</span>
<span id="L31"><span class="lineNum">      31</span>              :   static const String userProfile = 'user/current';</span>
<span id="L32"><span class="lineNum">      32</span>              :   static const String biometricToken = 'user/biometric-token';</span>
<span id="L33"><span class="lineNum">      33</span>              :   static const String deactivateAccountUrl = '/user/deactivate';</span>
<span id="L34"><span class="lineNum">      34</span>              :   static const String linkedCardsUrl = 'user/card/linked-cards';</span>
<span id="L35"><span class="lineNum">      35</span>              :   static const String paymentMethodsUrl = 'user/payment-methods';</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span>              :   // FaceOTP</span>
<span id="L38"><span class="lineNum">      38</span>              :   static const String checkLinkingCardStatusUrlV1 = 'user/card/check-linking-status';</span>
<span id="L39"><span class="lineNum">      39</span>              : </span>
<span id="L40"><span class="lineNum">      40</span>              :   // FaceAuth</span>
<span id="L41"><span class="lineNum">      41</span>              :   static const String checkLinkingCardStatusUrlV3 = 'v3/user/card/check-linking-status';</span>
<span id="L42"><span class="lineNum">      42</span>              : </span>
<span id="L43"><span class="lineNum">      43</span>              :   static const String submitLinkCardUrl = 'user/card/submit-link-card';</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :   static String linkCardSubmissionStatusCheckingUrl({String? requestId}) =&gt;</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :       '/user/card/submit-link-card-status?link_card_request_id=$requestId';</span></span>
<span id="L47"><span class="lineNum">      47</span>              :   static const String cardStatusUrl = 'user/card/status';</span>
<span id="L48"><span class="lineNum">      48</span>              :   static const String confirmDeleteAccountUrl = 'user/deletion-request/confirmation';</span>
<span id="L49"><span class="lineNum">      49</span>              :   static const String verifyDeletionRequestUrl = 'user/deletion-request/verification';</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span>              :   // Active card &amp; set pos limit</span>
<span id="L52"><span class="lineNum">      52</span>              :   static const String cardActivateUrl = 'user/card';</span>
<span id="L53"><span class="lineNum">      53</span>              :   static const String cardConfirmActivationUrl = '$cardActivateUrl/confirm-activation';</span>
<span id="L54"><span class="lineNum">      54</span>              :   static const String activateCardUrl = '$cardActivateUrl/activate';</span>
<span id="L55"><span class="lineNum">      55</span>              :   static const String setPOSLimitUrl = '$cardActivateUrl/set-pos-limit';</span>
<span id="L56"><span class="lineNum">      56</span>              :   static const String cardActivationStatus = '$cardActivateUrl/activation-status';</span>
<span id="L57"><span class="lineNum">      57</span>              : </span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :   UserRepoImpl(</span></span>
<span id="L59"><span class="lineNum">      59</span>              :     super.client,</span>
<span id="L60"><span class="lineNum">      60</span>              :     EvoLocalStorageHelper localStorageHelper,</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :   ) : super(</span></span>
<span id="L62"><span class="lineNum">      62</span>              :           localStorageHelper: localStorageHelper,</span>
<span id="L63"><span class="lineNum">      63</span>              :         );</span>
<span id="L64"><span class="lineNum">      64</span>              : </span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L66"><span class="lineNum">      66</span>              :   Future&lt;UserEntity&gt; getUserInfo({MockConfig? mockConfig}) async {</span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(userProfile, mockConfig: mockConfig);</span></span>
<span id="L68"><span class="lineNum">      68</span> <span class="tlaUNC">           0 :     final UserEntity userEntity = commonUtilFunction.serialize(</span></span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :             () =&gt; UserEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L70"><span class="lineNum">      70</span>              :             originalData: baseResponse) ??</span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :         UserEntity.unserializable();</span></span>
<span id="L72"><span class="lineNum">      72</span>              :     return userEntity;</span>
<span id="L73"><span class="lineNum">      73</span>              :   }</span>
<span id="L74"><span class="lineNum">      74</span>              : </span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L76"><span class="lineNum">      76</span>              :   Future&lt;SignInOtpEntity&gt; createPin({</span>
<span id="L77"><span class="lineNum">      77</span>              :     required String pin,</span>
<span id="L78"><span class="lineNum">      78</span>              :     required String? sessionToken,</span>
<span id="L79"><span class="lineNum">      79</span>              :     MockConfig? mockConfig,</span>
<span id="L80"><span class="lineNum">      80</span>              :   }) async {</span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; headers = &lt;String, dynamic&gt;{};</span></span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :     if (sessionToken?.isNotEmpty == true) {</span></span>
<span id="L83"><span class="lineNum">      83</span> <span class="tlaUNC">           0 :       headers.addAll(</span></span>
<span id="L84"><span class="lineNum">      84</span> <span class="tlaUNC">           0 :         &lt;String, String?&gt;{</span></span>
<span id="L85"><span class="lineNum">      85</span>              :           HeaderKey.sessionToken: sessionToken,</span>
<span id="L86"><span class="lineNum">      86</span>              :         },</span>
<span id="L87"><span class="lineNum">      87</span>              :       );</span>
<span id="L88"><span class="lineNum">      88</span>              :     }</span>
<span id="L89"><span class="lineNum">      89</span>              : </span>
<span id="L90"><span class="lineNum">      90</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.patch(</span></span>
<span id="L91"><span class="lineNum">      91</span>              :       createPinUrl,</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :       requestOption: CommonRequestOption(headers: headers),</span></span>
<span id="L93"><span class="lineNum">      93</span> <span class="tlaUNC">           0 :       data: &lt;String, dynamic&gt;{</span></span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :         TypeLogin.createPin.value: pin,</span></span>
<span id="L95"><span class="lineNum">      95</span>              :       },</span>
<span id="L96"><span class="lineNum">      96</span>              :       mockConfig: mockConfig,</span>
<span id="L97"><span class="lineNum">      97</span>              :     );</span>
<span id="L98"><span class="lineNum">      98</span>              : </span>
<span id="L99"><span class="lineNum">      99</span> <span class="tlaUNC">           0 :     final SignInOtpEntity signInOtpEntity = commonUtilFunction.serialize(</span></span>
<span id="L100"><span class="lineNum">     100</span> <span class="tlaUNC">           0 :             () =&gt; SignInOtpEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L101"><span class="lineNum">     101</span>              :             originalData: baseResponse) ??</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :         SignInOtpEntity.unserializable();</span></span>
<span id="L103"><span class="lineNum">     103</span>              : </span>
<span id="L104"><span class="lineNum">     104</span> <span class="tlaUNC">           0 :     if (signInOtpEntity.statusCode == CommonHttpClient.SUCCESS) {</span></span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :       await saveAuthenticationInfo(</span></span>
<span id="L106"><span class="lineNum">     106</span> <span class="tlaUNC">           0 :           accessToken: signInOtpEntity.accessToken,</span></span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :           refreshToken: signInOtpEntity.refreshToken,</span></span>
<span id="L108"><span class="lineNum">     108</span> <span class="tlaUNC">           0 :           deviceToken: signInOtpEntity.deviceToken,</span></span>
<span id="L109"><span class="lineNum">     109</span> <span class="tlaUNC">           0 :           userId: signInOtpEntity.userId,</span></span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :           notificationAuthKey: signInOtpEntity.notificationAuthKey);</span></span>
<span id="L111"><span class="lineNum">     111</span>              :     }</span>
<span id="L112"><span class="lineNum">     112</span>              :     return signInOtpEntity;</span>
<span id="L113"><span class="lineNum">     113</span>              :   }</span>
<span id="L114"><span class="lineNum">     114</span>              : </span>
<span id="L115"><span class="lineNum">     115</span>              :   /// TODO mock variable to test input pin failed</span>
<span id="L116"><span class="lineNum">     116</span>              :   int numberOfVerifiedFailed = 0;</span>
<span id="L117"><span class="lineNum">     117</span>              : </span>
<span id="L118"><span class="lineNum">     118</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L119"><span class="lineNum">     119</span>              :   Future&lt;BiometricTokenEntity&gt; getBiometricTokenByPin({String? pin, MockConfig? mockConfig}) async {</span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; data = &lt;String, dynamic&gt;{};</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :     if (pin != null &amp;&amp; pin.isNotEmpty) {</span></span>
<span id="L122"><span class="lineNum">     122</span> <span class="tlaUNC">           0 :       data['pin'] = pin;</span></span>
<span id="L123"><span class="lineNum">     123</span>              :     }</span>
<span id="L124"><span class="lineNum">     124</span>              : </span>
<span id="L125"><span class="lineNum">     125</span>              :     final BaseResponse baseResponse =</span>
<span id="L126"><span class="lineNum">     126</span> <span class="tlaUNC">           0 :         await client.post(biometricToken, data: data, mockConfig: mockConfig);</span></span>
<span id="L127"><span class="lineNum">     127</span>              : </span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :     final BiometricTokenEntity biometricTokenEntity = commonUtilFunction.serialize(</span></span>
<span id="L129"><span class="lineNum">     129</span> <span class="tlaUNC">           0 :             () =&gt; BiometricTokenEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L130"><span class="lineNum">     130</span>              :             originalData: baseResponse) ??</span>
<span id="L131"><span class="lineNum">     131</span> <span class="tlaUNC">           0 :         BiometricTokenEntity.unserializable();</span></span>
<span id="L132"><span class="lineNum">     132</span>              : </span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :     if (biometricTokenEntity.statusCode == CommonHttpClient.SUCCESS &amp;&amp;</span></span>
<span id="L134"><span class="lineNum">     134</span> <span class="tlaUNC">           0 :         !biometricTokenEntity.isNeedChallenge()) {</span></span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :       await saveAuthenticationInfo(</span></span>
<span id="L136"><span class="lineNum">     136</span> <span class="tlaUNC">           0 :         accessToken: biometricTokenEntity.accessToken,</span></span>
<span id="L137"><span class="lineNum">     137</span> <span class="tlaUNC">           0 :         refreshToken: biometricTokenEntity.refreshToken,</span></span>
<span id="L138"><span class="lineNum">     138</span> <span class="tlaUNC">           0 :         deviceToken: biometricTokenEntity.deviceToken,</span></span>
<span id="L139"><span class="lineNum">     139</span>              :       );</span>
<span id="L140"><span class="lineNum">     140</span>              :     }</span>
<span id="L141"><span class="lineNum">     141</span>              :     return biometricTokenEntity;</span>
<span id="L142"><span class="lineNum">     142</span>              :   }</span>
<span id="L143"><span class="lineNum">     143</span>              : </span>
<span id="L144"><span class="lineNum">     144</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L145"><span class="lineNum">     145</span>              :   Future&lt;BaseEntity&gt; deactivateAccount({MockConfig? mockConfig}) async {</span>
<span id="L146"><span class="lineNum">     146</span>              :     final BaseResponse baseResponse =</span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :         await client.patch(deactivateAccountUrl, mockConfig: mockConfig);</span></span>
<span id="L148"><span class="lineNum">     148</span> <span class="tlaUNC">           0 :     final BaseEntity baseEntity = commonUtilFunction.serialize(</span></span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :             () =&gt; BaseEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L150"><span class="lineNum">     150</span>              :             originalData: baseResponse) ??</span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :         BaseEntity(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L152"><span class="lineNum">     152</span>              :     return baseEntity;</span>
<span id="L153"><span class="lineNum">     153</span>              :   }</span>
<span id="L154"><span class="lineNum">     154</span>              : </span>
<span id="L155"><span class="lineNum">     155</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L156"><span class="lineNum">     156</span>              :   Future&lt;LinkedCardListEntity&gt; getLinkedCards({MockConfig? mockConfig}) async {</span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(linkedCardsUrl, mockConfig: mockConfig);</span></span>
<span id="L158"><span class="lineNum">     158</span> <span class="tlaUNC">           0 :     final LinkedCardListEntity linkedCardListEntity = commonUtilFunction.serialize(</span></span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :             () =&gt; LinkedCardListEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L160"><span class="lineNum">     160</span>              :             originalData: baseResponse) ??</span>
<span id="L161"><span class="lineNum">     161</span> <span class="tlaUNC">           0 :         LinkedCardListEntity.unserializable();</span></span>
<span id="L162"><span class="lineNum">     162</span>              :     return linkedCardListEntity;</span>
<span id="L163"><span class="lineNum">     163</span>              :   }</span>
<span id="L164"><span class="lineNum">     164</span>              : </span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L166"><span class="lineNum">     166</span>              :   Future&lt;PaymentMethodListEntity&gt; getPaymentMethods({MockConfig? mockConfig}) async {</span>
<span id="L167"><span class="lineNum">     167</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(paymentMethodsUrl, mockConfig: mockConfig);</span></span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaUNC">           0 :     final PaymentMethodListEntity storeEntity = commonUtilFunction.serialize(</span></span>
<span id="L169"><span class="lineNum">     169</span> <span class="tlaUNC">           0 :             () =&gt; PaymentMethodListEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L170"><span class="lineNum">     170</span>              :             originalData: baseResponse) ??</span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaUNC">           0 :         PaymentMethodListEntity.unserializable();</span></span>
<span id="L172"><span class="lineNum">     172</span>              :     return storeEntity;</span>
<span id="L173"><span class="lineNum">     173</span>              :   }</span>
<span id="L174"><span class="lineNum">     174</span>              : </span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L176"><span class="lineNum">     176</span>              :   Future&lt;LinkedCardStatusCheckingEntity&gt; checkLinkedCardsStatus({</span>
<span id="L177"><span class="lineNum">     177</span>              :     FacialVerificationVersion facialVerificationVersion = FacialVerificationVersion.version_3,</span>
<span id="L178"><span class="lineNum">     178</span>              :     MockConfig? mockConfig,</span>
<span id="L179"><span class="lineNum">     179</span>              :   }) async {</span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L181"><span class="lineNum">     181</span>              :       switch (facialVerificationVersion) {</span>
<span id="L182"><span class="lineNum">     182</span> <span class="tlaUNC">           0 :         FacialVerificationVersion.version_1 ||</span></span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :         FacialVerificationVersion.version_2 =&gt;</span></span>
<span id="L184"><span class="lineNum">     184</span>              :           checkLinkingCardStatusUrlV1,</span>
<span id="L185"><span class="lineNum">     185</span> <span class="tlaUNC">           0 :         FacialVerificationVersion.version_3 =&gt; checkLinkingCardStatusUrlV3,</span></span>
<span id="L186"><span class="lineNum">     186</span>              :       },</span>
<span id="L187"><span class="lineNum">     187</span>              :       mockConfig: mockConfig,</span>
<span id="L188"><span class="lineNum">     188</span>              :     );</span>
<span id="L189"><span class="lineNum">     189</span>              :     final LinkedCardStatusCheckingEntity baseEntity =</span>
<span id="L190"><span class="lineNum">     190</span> <span class="tlaUNC">           0 :         commonUtilFunction.serialize&lt;LinkedCardStatusCheckingEntity&gt;(</span></span>
<span id="L191"><span class="lineNum">     191</span> <span class="tlaUNC">           0 :               () =&gt; LinkedCardStatusCheckingEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L192"><span class="lineNum">     192</span>              :               originalData: baseResponse,</span>
<span id="L193"><span class="lineNum">     193</span>              :             ) ??</span>
<span id="L194"><span class="lineNum">     194</span> <span class="tlaUNC">           0 :             LinkedCardStatusCheckingEntity.unserializable();</span></span>
<span id="L195"><span class="lineNum">     195</span>              :     return baseEntity;</span>
<span id="L196"><span class="lineNum">     196</span>              :   }</span>
<span id="L197"><span class="lineNum">     197</span>              : </span>
<span id="L198"><span class="lineNum">     198</span>              :   /// mockFileName:</span>
<span id="L199"><span class="lineNum">     199</span>              :   /// submit_link_card_open_fail_screen.json</span>
<span id="L200"><span class="lineNum">     200</span>              :   /// submit_link_card_open_success_screen.json</span>
<span id="L201"><span class="lineNum">     201</span>              :   /// submit_link_card_open_three_d_secure.json</span>
<span id="L202"><span class="lineNum">     202</span>              :   /// submit_link_card_polling.json</span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L204"><span class="lineNum">     204</span>              :   Future&lt;SubmitLinkCardEntity&gt; submitLinkCard({</span>
<span id="L205"><span class="lineNum">     205</span>              :     required String? linkCardSession,</span>
<span id="L206"><span class="lineNum">     206</span>              :     required String? linkCardRequestId,</span>
<span id="L207"><span class="lineNum">     207</span>              :     MockConfig? mockConfig,</span>
<span id="L208"><span class="lineNum">     208</span>              :   }) async {</span>
<span id="L209"><span class="lineNum">     209</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L210"><span class="lineNum">     210</span>              :       submitLinkCardUrl,</span>
<span id="L211"><span class="lineNum">     211</span> <span class="tlaUNC">           0 :       data: &lt;String, dynamic&gt;{</span></span>
<span id="L212"><span class="lineNum">     212</span>              :         'link_card_request_id': linkCardRequestId,</span>
<span id="L213"><span class="lineNum">     213</span>              :       },</span>
<span id="L214"><span class="lineNum">     214</span> <span class="tlaUNC">           0 :       requestOption: CommonRequestOption(</span></span>
<span id="L215"><span class="lineNum">     215</span> <span class="tlaUNC">           0 :         headers: &lt;String, dynamic&gt;{</span></span>
<span id="L216"><span class="lineNum">     216</span>              :           HeaderKey.sessionToken: linkCardSession,</span>
<span id="L217"><span class="lineNum">     217</span>              :         },</span>
<span id="L218"><span class="lineNum">     218</span>              :       ),</span>
<span id="L219"><span class="lineNum">     219</span>              :       mockConfig: mockConfig,</span>
<span id="L220"><span class="lineNum">     220</span>              :     );</span>
<span id="L221"><span class="lineNum">     221</span>              : </span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaUNC">           0 :     final SubmitLinkCardEntity baseEntity = commonUtilFunction.serialize&lt;SubmitLinkCardEntity&gt;(</span></span>
<span id="L223"><span class="lineNum">     223</span> <span class="tlaUNC">           0 :           () =&gt; SubmitLinkCardEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L224"><span class="lineNum">     224</span>              :           originalData: baseResponse,</span>
<span id="L225"><span class="lineNum">     225</span>              :         ) ??</span>
<span id="L226"><span class="lineNum">     226</span> <span class="tlaUNC">           0 :         SubmitLinkCardEntity.unserializable();</span></span>
<span id="L227"><span class="lineNum">     227</span>              : </span>
<span id="L228"><span class="lineNum">     228</span>              :     return baseEntity;</span>
<span id="L229"><span class="lineNum">     229</span>              :   }</span>
<span id="L230"><span class="lineNum">     230</span>              : </span>
<span id="L231"><span class="lineNum">     231</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L232"><span class="lineNum">     232</span>              :   Future&lt;LinkCardSubmissionStatusEntity&gt; checkLinkCardSubmissionStatus({</span>
<span id="L233"><span class="lineNum">     233</span>              :     String? requestId,</span>
<span id="L234"><span class="lineNum">     234</span>              :     MockConfig? mockConfig,</span>
<span id="L235"><span class="lineNum">     235</span>              :   }) async {</span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L237"><span class="lineNum">     237</span> <span class="tlaUNC">           0 :       linkCardSubmissionStatusCheckingUrl(requestId: requestId),</span></span>
<span id="L238"><span class="lineNum">     238</span>              :       mockConfig: mockConfig,</span>
<span id="L239"><span class="lineNum">     239</span>              :     );</span>
<span id="L240"><span class="lineNum">     240</span>              :     final LinkCardSubmissionStatusEntity baseEntity =</span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaUNC">           0 :         commonUtilFunction.serialize&lt;LinkCardSubmissionStatusEntity&gt;(</span></span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaUNC">           0 :               () =&gt; LinkCardSubmissionStatusEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L243"><span class="lineNum">     243</span>              :               originalData: baseResponse,</span>
<span id="L244"><span class="lineNum">     244</span>              :             ) ??</span>
<span id="L245"><span class="lineNum">     245</span> <span class="tlaUNC">           0 :             LinkCardSubmissionStatusEntity.unserializable();</span></span>
<span id="L246"><span class="lineNum">     246</span>              :     return baseEntity;</span>
<span id="L247"><span class="lineNum">     247</span>              :   }</span>
<span id="L248"><span class="lineNum">     248</span>              : </span>
<span id="L249"><span class="lineNum">     249</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L250"><span class="lineNum">     250</span>              :   Future&lt;CardStatusEntity&gt; getCardStatus({</span>
<span id="L251"><span class="lineNum">     251</span>              :     bool renovateStatus = false,</span>
<span id="L252"><span class="lineNum">     252</span>              :     MockConfig? mockConfig,</span>
<span id="L253"><span class="lineNum">     253</span>              :   }) async {</span>
<span id="L254"><span class="lineNum">     254</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L255"><span class="lineNum">     255</span>              :       cardStatusUrl,</span>
<span id="L256"><span class="lineNum">     256</span> <span class="tlaUNC">           0 :       params: &lt;String, dynamic&gt;{</span></span>
<span id="L257"><span class="lineNum">     257</span>              :         'renovate_status': renovateStatus,</span>
<span id="L258"><span class="lineNum">     258</span>              :       },</span>
<span id="L259"><span class="lineNum">     259</span>              :       mockConfig: mockConfig,</span>
<span id="L260"><span class="lineNum">     260</span>              :     );</span>
<span id="L261"><span class="lineNum">     261</span> <span class="tlaUNC">           0 :     final CardStatusEntity entity = commonUtilFunction.serialize&lt;CardStatusEntity&gt;(</span></span>
<span id="L262"><span class="lineNum">     262</span> <span class="tlaUNC">           0 :           () =&gt; CardStatusEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L263"><span class="lineNum">     263</span>              :           originalData: baseResponse,</span>
<span id="L264"><span class="lineNum">     264</span>              :         ) ??</span>
<span id="L265"><span class="lineNum">     265</span> <span class="tlaUNC">           0 :         CardStatusEntity.unserializable();</span></span>
<span id="L266"><span class="lineNum">     266</span>              : </span>
<span id="L267"><span class="lineNum">     267</span>              :     return entity;</span>
<span id="L268"><span class="lineNum">     268</span>              :   }</span>
<span id="L269"><span class="lineNum">     269</span>              : </span>
<span id="L270"><span class="lineNum">     270</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L271"><span class="lineNum">     271</span>              :   Future&lt;UserDeletionVerificationEntity&gt; verifyUserDeletionRequest(</span>
<span id="L272"><span class="lineNum">     272</span>              :       {String? sessionToken, String? pin, MockConfig? mockConfig}) async {</span>
<span id="L273"><span class="lineNum">     273</span> <span class="tlaUNC">           0 :     final Map&lt;String, String?&gt; headers = &lt;String, String?&gt;{};</span></span>
<span id="L274"><span class="lineNum">     274</span>              :     if (sessionToken != null) {</span>
<span id="L275"><span class="lineNum">     275</span> <span class="tlaUNC">           0 :       headers[HeaderKey.sessionToken] = sessionToken;</span></span>
<span id="L276"><span class="lineNum">     276</span>              :     }</span>
<span id="L277"><span class="lineNum">     277</span>              : </span>
<span id="L278"><span class="lineNum">     278</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; dataMap = &lt;String, dynamic&gt;{};</span></span>
<span id="L279"><span class="lineNum">     279</span>              :     if (pin != null) {</span>
<span id="L280"><span class="lineNum">     280</span> <span class="tlaUNC">           0 :       dataMap['pin'] = pin;</span></span>
<span id="L281"><span class="lineNum">     281</span>              :     }</span>
<span id="L282"><span class="lineNum">     282</span>              : </span>
<span id="L283"><span class="lineNum">     283</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L284"><span class="lineNum">     284</span>              :       verifyDeletionRequestUrl,</span>
<span id="L285"><span class="lineNum">     285</span> <span class="tlaUNC">           0 :       requestOption: CommonRequestOption(headers: headers),</span></span>
<span id="L286"><span class="lineNum">     286</span>              :       mockConfig: mockConfig,</span>
<span id="L287"><span class="lineNum">     287</span>              :       data: dataMap,</span>
<span id="L288"><span class="lineNum">     288</span>              :     );</span>
<span id="L289"><span class="lineNum">     289</span>              : </span>
<span id="L290"><span class="lineNum">     290</span>              :     final UserDeletionVerificationEntity entity =</span>
<span id="L291"><span class="lineNum">     291</span> <span class="tlaUNC">           0 :         commonUtilFunction.serialize&lt;UserDeletionVerificationEntity&gt;(</span></span>
<span id="L292"><span class="lineNum">     292</span> <span class="tlaUNC">           0 :               () =&gt; UserDeletionVerificationEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L293"><span class="lineNum">     293</span>              :               originalData: baseResponse,</span>
<span id="L294"><span class="lineNum">     294</span>              :             ) ??</span>
<span id="L295"><span class="lineNum">     295</span> <span class="tlaUNC">           0 :             UserDeletionVerificationEntity.unserializable();</span></span>
<span id="L296"><span class="lineNum">     296</span>              : </span>
<span id="L297"><span class="lineNum">     297</span>              :     return entity;</span>
<span id="L298"><span class="lineNum">     298</span>              :   }</span>
<span id="L299"><span class="lineNum">     299</span>              : </span>
<span id="L300"><span class="lineNum">     300</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L301"><span class="lineNum">     301</span>              :   Future&lt;BaseEntity&gt; confirmDeleteAccount({</span>
<span id="L302"><span class="lineNum">     302</span>              :     required String? sessionToken,</span>
<span id="L303"><span class="lineNum">     303</span>              :     required List&lt;int&gt;? reasons,</span>
<span id="L304"><span class="lineNum">     304</span>              :     MockConfig? mockConfig,</span>
<span id="L305"><span class="lineNum">     305</span>              :   }) async {</span>
<span id="L306"><span class="lineNum">     306</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; headers = &lt;String, String?&gt;{</span></span>
<span id="L307"><span class="lineNum">     307</span>              :       HeaderKey.sessionToken: sessionToken,</span>
<span id="L308"><span class="lineNum">     308</span>              :     };</span>
<span id="L309"><span class="lineNum">     309</span>              : </span>
<span id="L310"><span class="lineNum">     310</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; data = &lt;String, dynamic&gt;{</span></span>
<span id="L311"><span class="lineNum">     311</span>              :       'reasons': reasons,</span>
<span id="L312"><span class="lineNum">     312</span>              :     };</span>
<span id="L313"><span class="lineNum">     313</span>              : </span>
<span id="L314"><span class="lineNum">     314</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L315"><span class="lineNum">     315</span>              :       confirmDeleteAccountUrl,</span>
<span id="L316"><span class="lineNum">     316</span> <span class="tlaUNC">           0 :       requestOption: CommonRequestOption(headers: headers),</span></span>
<span id="L317"><span class="lineNum">     317</span>              :       data: data,</span>
<span id="L318"><span class="lineNum">     318</span>              :       mockConfig: mockConfig,</span>
<span id="L319"><span class="lineNum">     319</span>              :     );</span>
<span id="L320"><span class="lineNum">     320</span> <span class="tlaUNC">           0 :     final BaseEntity baseEntity = commonUtilFunction.serialize(</span></span>
<span id="L321"><span class="lineNum">     321</span> <span class="tlaUNC">           0 :             () =&gt; BaseEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L322"><span class="lineNum">     322</span>              :             originalData: baseResponse) ??</span>
<span id="L323"><span class="lineNum">     323</span> <span class="tlaUNC">           0 :         BaseEntity(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L324"><span class="lineNum">     324</span>              :     return baseEntity;</span>
<span id="L325"><span class="lineNum">     325</span>              :   }</span>
<span id="L326"><span class="lineNum">     326</span>              : </span>
<span id="L327"><span class="lineNum">     327</span>              :   /// Active Card &amp; POS Limit</span>
<span id="L328"><span class="lineNum">     328</span>              : </span>
<span id="L329"><span class="lineNum">     329</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L330"><span class="lineNum">     330</span>              :   Future&lt;BaseEntity&gt; cardConfirmActivation({</span>
<span id="L331"><span class="lineNum">     331</span>              :     required String platform,</span>
<span id="L332"><span class="lineNum">     332</span>              :     MockConfig? mockConfig,</span>
<span id="L333"><span class="lineNum">     333</span>              :   }) async {</span>
<span id="L334"><span class="lineNum">     334</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.patch(</span></span>
<span id="L335"><span class="lineNum">     335</span>              :       cardConfirmActivationUrl,</span>
<span id="L336"><span class="lineNum">     336</span> <span class="tlaUNC">           0 :       data: &lt;String, dynamic&gt;{</span></span>
<span id="L337"><span class="lineNum">     337</span>              :         'platform': platform,</span>
<span id="L338"><span class="lineNum">     338</span>              :       },</span>
<span id="L339"><span class="lineNum">     339</span>              :       mockConfig: mockConfig,</span>
<span id="L340"><span class="lineNum">     340</span>              :     );</span>
<span id="L341"><span class="lineNum">     341</span>              : </span>
<span id="L342"><span class="lineNum">     342</span> <span class="tlaUNC">           0 :     final BaseEntity baseEntity = commonUtilFunction.serialize(</span></span>
<span id="L343"><span class="lineNum">     343</span> <span class="tlaUNC">           0 :             () =&gt; BaseEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L344"><span class="lineNum">     344</span>              :             originalData: baseResponse) ??</span>
<span id="L345"><span class="lineNum">     345</span> <span class="tlaUNC">           0 :         BaseEntity(localExceptionCode: CommonHttpClient.INVALID_FORMAT);</span></span>
<span id="L346"><span class="lineNum">     346</span>              : </span>
<span id="L347"><span class="lineNum">     347</span>              :     return baseEntity;</span>
<span id="L348"><span class="lineNum">     348</span>              :   }</span>
<span id="L349"><span class="lineNum">     349</span>              : </span>
<span id="L350"><span class="lineNum">     350</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L351"><span class="lineNum">     351</span>              :   Future&lt;CardActivateEntity&gt; activateCard({</span>
<span id="L352"><span class="lineNum">     352</span>              :     required int? posLimit,</span>
<span id="L353"><span class="lineNum">     353</span>              :     MockConfig? mockConfig,</span>
<span id="L354"><span class="lineNum">     354</span>              :   }) async {</span>
<span id="L355"><span class="lineNum">     355</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; data = &lt;String, dynamic&gt;{</span></span>
<span id="L356"><span class="lineNum">     356</span>              :       'pos_limit': posLimit,</span>
<span id="L357"><span class="lineNum">     357</span>              :     };</span>
<span id="L358"><span class="lineNum">     358</span>              : </span>
<span id="L359"><span class="lineNum">     359</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L360"><span class="lineNum">     360</span>              :       activateCardUrl,</span>
<span id="L361"><span class="lineNum">     361</span>              :       data: data,</span>
<span id="L362"><span class="lineNum">     362</span>              :       mockConfig: mockConfig,</span>
<span id="L363"><span class="lineNum">     363</span>              :     );</span>
<span id="L364"><span class="lineNum">     364</span>              : </span>
<span id="L365"><span class="lineNum">     365</span> <span class="tlaUNC">           0 :     final CardActivateEntity cardActivateEntity = commonUtilFunction.serialize(</span></span>
<span id="L366"><span class="lineNum">     366</span> <span class="tlaUNC">           0 :           () =&gt; CardActivateEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L367"><span class="lineNum">     367</span>              :           originalData: baseResponse,</span>
<span id="L368"><span class="lineNum">     368</span>              :         ) ??</span>
<span id="L369"><span class="lineNum">     369</span> <span class="tlaUNC">           0 :         CardActivateEntity.unserializable();</span></span>
<span id="L370"><span class="lineNum">     370</span>              : </span>
<span id="L371"><span class="lineNum">     371</span>              :     return cardActivateEntity;</span>
<span id="L372"><span class="lineNum">     372</span>              :   }</span>
<span id="L373"><span class="lineNum">     373</span>              : </span>
<span id="L374"><span class="lineNum">     374</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L375"><span class="lineNum">     375</span>              :   Future&lt;SetPOSLimitEntity&gt; setPOSLimit({required int? posLimit, MockConfig? mockConfig}) async {</span>
<span id="L376"><span class="lineNum">     376</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; data = &lt;String, dynamic&gt;{</span></span>
<span id="L377"><span class="lineNum">     377</span>              :       'pos_limit': posLimit,</span>
<span id="L378"><span class="lineNum">     378</span>              :     };</span>
<span id="L379"><span class="lineNum">     379</span>              : </span>
<span id="L380"><span class="lineNum">     380</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L381"><span class="lineNum">     381</span>              :       setPOSLimitUrl,</span>
<span id="L382"><span class="lineNum">     382</span>              :       data: data,</span>
<span id="L383"><span class="lineNum">     383</span>              :       mockConfig: mockConfig,</span>
<span id="L384"><span class="lineNum">     384</span>              :     );</span>
<span id="L385"><span class="lineNum">     385</span>              : </span>
<span id="L386"><span class="lineNum">     386</span> <span class="tlaUNC">           0 :     final SetPOSLimitEntity setPOSLimitEntity = commonUtilFunction.serialize(</span></span>
<span id="L387"><span class="lineNum">     387</span> <span class="tlaUNC">           0 :           () =&gt; SetPOSLimitEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L388"><span class="lineNum">     388</span>              :           originalData: baseResponse,</span>
<span id="L389"><span class="lineNum">     389</span>              :         ) ??</span>
<span id="L390"><span class="lineNum">     390</span> <span class="tlaUNC">           0 :         SetPOSLimitEntity.unserializable();</span></span>
<span id="L391"><span class="lineNum">     391</span>              : </span>
<span id="L392"><span class="lineNum">     392</span>              :     return setPOSLimitEntity;</span>
<span id="L393"><span class="lineNum">     393</span>              :   }</span>
<span id="L394"><span class="lineNum">     394</span>              : </span>
<span id="L395"><span class="lineNum">     395</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L396"><span class="lineNum">     396</span>              :   Future&lt;CardActivationStatusEntity&gt; getCardActivationStatus({MockConfig? mockConfig}) async {</span>
<span id="L397"><span class="lineNum">     397</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L398"><span class="lineNum">     398</span>              :       cardActivationStatus,</span>
<span id="L399"><span class="lineNum">     399</span>              :       mockConfig: mockConfig,</span>
<span id="L400"><span class="lineNum">     400</span>              :     );</span>
<span id="L401"><span class="lineNum">     401</span>              :     final CardActivationStatusEntity entity =</span>
<span id="L402"><span class="lineNum">     402</span> <span class="tlaUNC">           0 :         commonUtilFunction.serialize&lt;CardActivationStatusEntity&gt;(</span></span>
<span id="L403"><span class="lineNum">     403</span> <span class="tlaUNC">           0 :               () =&gt; CardActivationStatusEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L404"><span class="lineNum">     404</span>              :               originalData: baseResponse,</span>
<span id="L405"><span class="lineNum">     405</span>              :             ) ??</span>
<span id="L406"><span class="lineNum">     406</span> <span class="tlaUNC">           0 :             CardActivationStatusEntity.unserializable();</span></span>
<span id="L407"><span class="lineNum">     407</span>              : </span>
<span id="L408"><span class="lineNum">     408</span>              :     return entity;</span>
<span id="L409"><span class="lineNum">     409</span>              :   }</span>
<span id="L410"><span class="lineNum">     410</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
