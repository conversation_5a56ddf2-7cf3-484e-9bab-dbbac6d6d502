<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/repository</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - lib/data/repository</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">585</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <center>
          <table width="80%" cellpadding=1 cellspacing=1 border=0>

            <tr>
              <td width="40%"><br></td>
            <td width="15%"></td>
            <td width="15%"></td>
            <td width="15%"></td>
            <td width="15%"></td>
            </tr>

            <tr>
              <td class="tableHead" rowspan=2>File <span  title="Click to sort table by file name" class="tableHeadSort"><a href="index.html"><img src="../../../updown.png" width=10 height=14 alt="Sort by file name" title="Click to sort table by file name" border=0></a></span></td>
        <td class="tableHead" colspan=4>Line Coverage <span  title="Click to sort table by line coverage" class="tableHeadSort"><img src="../../../glass.png" width=10 height=14 alt="Sort by line coverage" title="Click to sort table by line coverage" border=0></span></td>
            </tr>
            <tr>
                    <td class="tableHead" colspan=2> Rate</td>
                    <td class="tableHead"> Total</td>
                    <td class="tableHead"> Hit</td>
            </tr>
            <tr>
              <td class="coverFile"><a href="common_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/common_repo_impl.dart">common_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">6</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="merchant_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/merchant_repo_impl.dart">merchant_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">6</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="qr_code_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/qr_code_repo_impl.dart">qr_code_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">7</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="referral_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/referral_repo_impl.dart">referral_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">7</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="cashback_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/cashback_repo_impl.dart">cashback_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">13</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="base_repo.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/base_repo.dart">base_repo.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">15</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="decree_consent_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/decree_consent_repo_impl.dart">decree_consent_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">17</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="announcement_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/announcement_repo_impl.dart">announcement_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">18</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="home_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/home_repo_impl.dart">home_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">21</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="emi_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/emi_repo_impl.dart">emi_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">24</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="mock_test_config_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/mock_test_config_repo_impl.dart">mock_test_config_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">26</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="ekyc_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/ekyc_repo_impl.dart">ekyc_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">29</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="campaign_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/campaign_repo_impl.dart">campaign_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">32</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="firebase_remote_config_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/firebase_remote_config_impl.dart">firebase_remote_config_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">35</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="checkout_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/checkout_repo_impl.dart">checkout_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">77</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="authentication_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/authentication_repo_impl.dart">authentication_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">126</td>
              <td class="coverNumDflt"></td>
            </tr>
            <tr>
              <td class="coverFile"><a href="user_repo_impl.dart.gcov.html" title="Click to go to file /Users/<USER>/Projects/EVO/evo-app/lib/data/repository/user_repo_impl.dart">user_repo_impl.dart</a></td>
              <td class="coverBar" align="center">
                                <table border=0 cellspacing=0 cellpadding=1><tr><td class="coverBarOutline"><img src="../../../snow.png" width=100 height=10 alt="0.0%"></td></tr></table>
              </td>
              <td class="coverPerLo">0.0&nbsp;%</td>
              <td class="coverNumDflt">126</td>
              <td class="coverNumDflt"></td>
            </tr>
        <tr>
          <td class="footnote" colspan=5>Note:  'Function Coverage' columns elided as function owner is not identified.</td>
         </tr>
          </table>
          </center>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
