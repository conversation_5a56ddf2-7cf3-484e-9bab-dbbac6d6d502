<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/repository/dop_native_repo/dop_native_repo_impl.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/repository/dop_native_repo">lib/data/repository/dop_native_repo</a> - dop_native_repo_impl.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">130</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/response/base_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../request/dop_native/dop_native_application_submit_form_request.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../../request/dop_native/dop_native_collect_location_request.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../../request/dop_native/dop_native_esign_form_request.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../../request/dop_native/dop_native_log_event_data_request.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../../response/dop_native/dop_native_activate_card_entity.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../../response/dop_native/dop_native_application_form_data_entity.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../../response/dop_native/dop_native_application_state_entity.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../../response/dop_native/dop_native_appraising_status_entity.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../../response/dop_native/dop_native_bootstrap_auth_settings_entity.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../../response/dop_native/dop_native_bootstrap_entity.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../../response/dop_native/dop_native_card_status_entity.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../../response/dop_native/dop_native_credit_assignment_entity.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../../response/dop_native/dop_native_esign_prepare_entity.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import '../../response/dop_native/dop_native_metadata_entity.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import '../../response/dop_native/dop_native_metadata_suggestion_entity.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : import '../../response/dop_native/dop_native_register_entity.dart';</span>
<span id="L22"><span class="lineNum">      22</span>              : import '../../response/dop_native/dop_native_registration_campaign_entity.dart';</span>
<span id="L23"><span class="lineNum">      23</span>              : import '../../response/dop_native/dop_native_request_otp_entity.dart';</span>
<span id="L24"><span class="lineNum">      24</span>              : import '../../response/dop_native/dop_verify_otp_entity.dart';</span>
<span id="L25"><span class="lineNum">      25</span>              : import '../../response/dop_native/e_sign_state_entity.dart';</span>
<span id="L26"><span class="lineNum">      26</span>              : import '../base_repo.dart';</span>
<span id="L27"><span class="lineNum">      27</span>              : import 'dop_native_repo.dart';</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              : class DOPNativeRepoImpl extends BaseRepo implements DOPNativeRepo {</span>
<span id="L30"><span class="lineNum">      30</span>              :   static const String dopNativeBaseUrl = 'dop/api';</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span>              :   static const String baseOTPURL = '$dopNativeBaseUrl/otp';</span>
<span id="L33"><span class="lineNum">      33</span>              :   static const String baseBootstrapURL = '$dopNativeBaseUrl/bootstrap';</span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span>              :   static const String campaignUrl = '$dopNativeBaseUrl/registration/campaign';</span>
<span id="L36"><span class="lineNum">      36</span>              :   static const String registerUrl = '$dopNativeBaseUrl/registration/register';</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span>              :   /// OTP</span>
<span id="L39"><span class="lineNum">      39</span>              :   static const String requestOTPURL = '$baseOTPURL/send';</span>
<span id="L40"><span class="lineNum">      40</span>              :   static const String verifyOTPURL = '$baseOTPURL/verify';</span>
<span id="L41"><span class="lineNum">      41</span>              : </span>
<span id="L42"><span class="lineNum">      42</span>              :   /// Bootstrap</span>
<span id="L43"><span class="lineNum">      43</span>              :   static const String getBootstrapAuthSettingsURL = '$baseBootstrapURL/auth_settings';</span>
<span id="L44"><span class="lineNum">      44</span>              : </span>
<span id="L45"><span class="lineNum">      45</span>              :   /// Application State</span>
<span id="L46"><span class="lineNum">      46</span>              :   static const String getApplicationStateURL = '/dop/application/state';</span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span>              :   /// Metadata</span>
<span id="L49"><span class="lineNum">      49</span>              :   static const String metadataURL = '$dopNativeBaseUrl/metadata';</span>
<span id="L50"><span class="lineNum">      50</span>              : </span>
<span id="L51"><span class="lineNum">      51</span>              :   /// Metadata Suggestion</span>
<span id="L52"><span class="lineNum">      52</span>              :   static const String metadataSuggestionURL = '$dopNativeBaseUrl/metadata/suggest';</span>
<span id="L53"><span class="lineNum">      53</span>              : </span>
<span id="L54"><span class="lineNum">      54</span>              :   /// Appraising Status</span>
<span id="L55"><span class="lineNum">      55</span>              :   static const String appraisingStatusURL = '$dopNativeBaseUrl/appraising/status';</span>
<span id="L56"><span class="lineNum">      56</span>              : </span>
<span id="L57"><span class="lineNum">      57</span>              :   /// Credit assignment</span>
<span id="L58"><span class="lineNum">      58</span>              :   static const String appraisingCreditAssignment = '$dopNativeBaseUrl/appraising/credit_assignment';</span>
<span id="L59"><span class="lineNum">      59</span>              : </span>
<span id="L60"><span class="lineNum">      60</span>              :   /// CIF confirm</span>
<span id="L61"><span class="lineNum">      61</span>              :   static const String cifConfirmUrl = '$dopNativeBaseUrl/cif/confirm';</span>
<span id="L62"><span class="lineNum">      62</span>              : </span>
<span id="L63"><span class="lineNum">      63</span>              :   /// Form Data</span>
<span id="L64"><span class="lineNum">      64</span>              :   static const String applicationFormDataURL = '$dopNativeBaseUrl/application/form_data';</span>
<span id="L65"><span class="lineNum">      65</span>              : </span>
<span id="L66"><span class="lineNum">      66</span>              :   /// Application next state</span>
<span id="L67"><span class="lineNum">      67</span>              :   static const String getApplicationNextStateUrl = '$dopNativeBaseUrl/application/state/next';</span>
<span id="L68"><span class="lineNum">      68</span>              : </span>
<span id="L69"><span class="lineNum">      69</span>              :   /// Submit form application</span>
<span id="L70"><span class="lineNum">      70</span>              :   static const String submitFormApplicationUrl = '$dopNativeBaseUrl/application/form/submit';</span>
<span id="L71"><span class="lineNum">      71</span>              : </span>
<span id="L72"><span class="lineNum">      72</span>              :   /// eSign</span>
<span id="L73"><span class="lineNum">      73</span>              :   static const String getESignStateUrl = '$dopNativeBaseUrl/esign/state';</span>
<span id="L74"><span class="lineNum">      74</span>              :   static const String submitESignIntroNextUrl = '$dopNativeBaseUrl/esign/intro/next';</span>
<span id="L75"><span class="lineNum">      75</span>              : </span>
<span id="L76"><span class="lineNum">      76</span>              :   /// Get card status</span>
<span id="L77"><span class="lineNum">      77</span>              :   static const String cardStatusUrl = '$dopNativeBaseUrl/card/status';</span>
<span id="L78"><span class="lineNum">      78</span>              : </span>
<span id="L79"><span class="lineNum">      79</span>              :   /// Activate card</span>
<span id="L80"><span class="lineNum">      80</span>              :   static const String activateCardUrl = '$dopNativeBaseUrl/card/activate';</span>
<span id="L81"><span class="lineNum">      81</span>              : </span>
<span id="L82"><span class="lineNum">      82</span>              :   /// Submit e-sign form</span>
<span id="L83"><span class="lineNum">      83</span>              :   static const String submitESignUrl = '$dopNativeBaseUrl/esign/form/submit';</span>
<span id="L84"><span class="lineNum">      84</span>              : </span>
<span id="L85"><span class="lineNum">      85</span>              :   /// Prepare e-sign</span>
<span id="L86"><span class="lineNum">      86</span>              :   static const String eSignPrepareUrl = '$dopNativeBaseUrl/esign/prepare';</span>
<span id="L87"><span class="lineNum">      87</span>              : </span>
<span id="L88"><span class="lineNum">      88</span>              :   /// Log event</span>
<span id="L89"><span class="lineNum">      89</span>              :   static const String logEventUrl = '$dopNativeBaseUrl/event/track';</span>
<span id="L90"><span class="lineNum">      90</span>              : </span>
<span id="L91"><span class="lineNum">      91</span>              :   /// Collect Location</span>
<span id="L92"><span class="lineNum">      92</span>              :   static const String collectLocationUrl = '$dopNativeBaseUrl/collect_extra/location';</span>
<span id="L93"><span class="lineNum">      93</span>              : </span>
<span id="L94"><span class="lineNum">      94</span> <span class="tlaUNC">           0 :   DOPNativeRepoImpl(super.client);</span></span>
<span id="L95"><span class="lineNum">      95</span>              : </span>
<span id="L96"><span class="lineNum">      96</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L97"><span class="lineNum">      97</span>              :   Future&lt;DOPNativeRegistrationCampaignEntity&gt; registrationCampaign({</span>
<span id="L98"><span class="lineNum">      98</span>              :     String? campaignCode,</span>
<span id="L99"><span class="lineNum">      99</span>              :     String leadSource = LeadSource.evoNative,</span>
<span id="L100"><span class="lineNum">     100</span>              :     MockConfig? mockConfig,</span>
<span id="L101"><span class="lineNum">     101</span>              :   }) async {</span>
<span id="L102"><span class="lineNum">     102</span> <span class="tlaUNC">           0 :     final Map&lt;String, String&gt; params = &lt;String, String&gt;{</span></span>
<span id="L103"><span class="lineNum">     103</span>              :       'partner': leadSource,</span>
<span id="L104"><span class="lineNum">     104</span>              :     };</span>
<span id="L105"><span class="lineNum">     105</span>              : </span>
<span id="L106"><span class="lineNum">     106</span>              :     if (campaignCode != null) {</span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :       params['campaign_code'] = campaignCode;</span></span>
<span id="L108"><span class="lineNum">     108</span>              :     }</span>
<span id="L109"><span class="lineNum">     109</span>              : </span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L111"><span class="lineNum">     111</span>              :       campaignUrl,</span>
<span id="L112"><span class="lineNum">     112</span>              :       params: params,</span>
<span id="L113"><span class="lineNum">     113</span>              :       mockConfig: mockConfig,</span>
<span id="L114"><span class="lineNum">     114</span>              :     );</span>
<span id="L115"><span class="lineNum">     115</span>              : </span>
<span id="L116"><span class="lineNum">     116</span> <span class="tlaUNC">           0 :     final DOPNativeRegistrationCampaignEntity registrationCampaignEntity = commonUtilFunction</span></span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :             .serialize(() =&gt; DOPNativeRegistrationCampaignEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L118"><span class="lineNum">     118</span>              :                 originalData: baseResponse) ??</span>
<span id="L119"><span class="lineNum">     119</span> <span class="tlaUNC">           0 :         DOPNativeRegistrationCampaignEntity.unserializable();</span></span>
<span id="L120"><span class="lineNum">     120</span>              :     return registrationCampaignEntity;</span>
<span id="L121"><span class="lineNum">     121</span>              :   }</span>
<span id="L122"><span class="lineNum">     122</span>              : </span>
<span id="L123"><span class="lineNum">     123</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L124"><span class="lineNum">     124</span>              :   Future&lt;DOPNativeRegisterEntity&gt; register({</span>
<span id="L125"><span class="lineNum">     125</span>              :     required String phoneNumber,</span>
<span id="L126"><span class="lineNum">     126</span>              :     required String campaignCode,</span>
<span id="L127"><span class="lineNum">     127</span>              :     required String source,</span>
<span id="L128"><span class="lineNum">     128</span>              :     required String signature,</span>
<span id="L129"><span class="lineNum">     129</span>              :     required String platform,</span>
<span id="L130"><span class="lineNum">     130</span>              :     Map&lt;String, dynamic&gt;? collectedData,</span>
<span id="L131"><span class="lineNum">     131</span>              :     Map&lt;String, dynamic&gt;? params,</span>
<span id="L132"><span class="lineNum">     132</span>              :     bool allowedSwitchFlow = false,</span>
<span id="L133"><span class="lineNum">     133</span>              :     MockConfig? mockConfig,</span>
<span id="L134"><span class="lineNum">     134</span>              :   }) async {</span>
<span id="L135"><span class="lineNum">     135</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; body = &lt;String, dynamic&gt;{</span></span>
<span id="L136"><span class="lineNum">     136</span>              :       'phone_number': phoneNumber,</span>
<span id="L137"><span class="lineNum">     137</span>              :       'campaign_code': campaignCode,</span>
<span id="L138"><span class="lineNum">     138</span>              :       'source': source,</span>
<span id="L139"><span class="lineNum">     139</span>              :       'allowed_switch_flow': allowedSwitchFlow,</span>
<span id="L140"><span class="lineNum">     140</span>              :     };</span>
<span id="L141"><span class="lineNum">     141</span>              : </span>
<span id="L142"><span class="lineNum">     142</span>              :     if (collectedData != null) {</span>
<span id="L143"><span class="lineNum">     143</span> <span class="tlaUNC">           0 :       body['collected_data'] = collectedData;</span></span>
<span id="L144"><span class="lineNum">     144</span>              :     }</span>
<span id="L145"><span class="lineNum">     145</span>              : </span>
<span id="L146"><span class="lineNum">     146</span>              :     if (params != null) {</span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :       body['params'] = params;</span></span>
<span id="L148"><span class="lineNum">     148</span>              :     }</span>
<span id="L149"><span class="lineNum">     149</span>              : </span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :     body['signature'] = signature;</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :     body['platform'] = platform;</span></span>
<span id="L152"><span class="lineNum">     152</span>              : </span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L154"><span class="lineNum">     154</span>              :       registerUrl,</span>
<span id="L155"><span class="lineNum">     155</span>              :       data: body,</span>
<span id="L156"><span class="lineNum">     156</span>              :       mockConfig: mockConfig,</span>
<span id="L157"><span class="lineNum">     157</span>              :     );</span>
<span id="L158"><span class="lineNum">     158</span>              : </span>
<span id="L159"><span class="lineNum">     159</span> <span class="tlaUNC">           0 :     final DOPNativeRegisterEntity registerEntity = commonUtilFunction.serialize(</span></span>
<span id="L160"><span class="lineNum">     160</span> <span class="tlaUNC">           0 :             () =&gt; DOPNativeRegisterEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L161"><span class="lineNum">     161</span>              :             originalData: baseResponse) ??</span>
<span id="L162"><span class="lineNum">     162</span> <span class="tlaUNC">           0 :         DOPNativeRegisterEntity.unserializable();</span></span>
<span id="L163"><span class="lineNum">     163</span>              :     return registerEntity;</span>
<span id="L164"><span class="lineNum">     164</span>              :   }</span>
<span id="L165"><span class="lineNum">     165</span>              : </span>
<span id="L166"><span class="lineNum">     166</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L167"><span class="lineNum">     167</span>              :   Future&lt;DOPNativeRequestOTPEntity&gt; requestOTP({</span>
<span id="L168"><span class="lineNum">     168</span>              :     required String? token,</span>
<span id="L169"><span class="lineNum">     169</span>              :     MockConfig? mockConfig,</span>
<span id="L170"><span class="lineNum">     170</span>              :   }) async {</span>
<span id="L171"><span class="lineNum">     171</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L172"><span class="lineNum">     172</span>              :       requestOTPURL,</span>
<span id="L173"><span class="lineNum">     173</span> <span class="tlaUNC">           0 :       data: &lt;String, dynamic&gt;{</span></span>
<span id="L174"><span class="lineNum">     174</span>              :         'token': token,</span>
<span id="L175"><span class="lineNum">     175</span>              :       },</span>
<span id="L176"><span class="lineNum">     176</span>              :       mockConfig: mockConfig,</span>
<span id="L177"><span class="lineNum">     177</span>              :     );</span>
<span id="L178"><span class="lineNum">     178</span>              : </span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :     final DOPNativeRequestOTPEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L180"><span class="lineNum">     180</span> <span class="tlaUNC">           0 :           () =&gt; DOPNativeRequestOTPEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L181"><span class="lineNum">     181</span>              :           originalData: baseResponse,</span>
<span id="L182"><span class="lineNum">     182</span>              :         ) ??</span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :         DOPNativeRequestOTPEntity.unserializable();</span></span>
<span id="L184"><span class="lineNum">     184</span>              :     return entity;</span>
<span id="L185"><span class="lineNum">     185</span>              :   }</span>
<span id="L186"><span class="lineNum">     186</span>              : </span>
<span id="L187"><span class="lineNum">     187</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L188"><span class="lineNum">     188</span>              :   Future&lt;DOPNativeBootstrapEntity&gt; getBootstrapInfo({</span>
<span id="L189"><span class="lineNum">     189</span>              :     required String? token,</span>
<span id="L190"><span class="lineNum">     190</span>              :     MockConfig? mockConfig,</span>
<span id="L191"><span class="lineNum">     191</span>              :   }) async {</span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; params = &lt;String, dynamic&gt;{</span></span>
<span id="L193"><span class="lineNum">     193</span>              :       'token': token,</span>
<span id="L194"><span class="lineNum">     194</span>              :     };</span>
<span id="L195"><span class="lineNum">     195</span>              : </span>
<span id="L196"><span class="lineNum">     196</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L197"><span class="lineNum">     197</span>              :       baseBootstrapURL,</span>
<span id="L198"><span class="lineNum">     198</span>              :       params: params,</span>
<span id="L199"><span class="lineNum">     199</span>              :       mockConfig: mockConfig,</span>
<span id="L200"><span class="lineNum">     200</span>              :     );</span>
<span id="L201"><span class="lineNum">     201</span>              : </span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaUNC">           0 :     final DOPNativeBootstrapEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L203"><span class="lineNum">     203</span> <span class="tlaUNC">           0 :           () =&gt; DOPNativeBootstrapEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L204"><span class="lineNum">     204</span>              :           originalData: baseResponse,</span>
<span id="L205"><span class="lineNum">     205</span>              :         ) ??</span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaUNC">           0 :         DOPNativeBootstrapEntity.unserializable();</span></span>
<span id="L207"><span class="lineNum">     207</span>              :     return entity;</span>
<span id="L208"><span class="lineNum">     208</span>              :   }</span>
<span id="L209"><span class="lineNum">     209</span>              : </span>
<span id="L210"><span class="lineNum">     210</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L211"><span class="lineNum">     211</span>              :   Future&lt;DOPNativeBootstrapAuthSettingsEntity&gt; getBootstrapAuthSettings({</span>
<span id="L212"><span class="lineNum">     212</span>              :     required String? token,</span>
<span id="L213"><span class="lineNum">     213</span>              :     MockConfig? mockConfig,</span>
<span id="L214"><span class="lineNum">     214</span>              :   }) async {</span>
<span id="L215"><span class="lineNum">     215</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; params = &lt;String, dynamic&gt;{</span></span>
<span id="L216"><span class="lineNum">     216</span>              :       'token': token,</span>
<span id="L217"><span class="lineNum">     217</span>              :     };</span>
<span id="L218"><span class="lineNum">     218</span>              : </span>
<span id="L219"><span class="lineNum">     219</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L220"><span class="lineNum">     220</span>              :       getBootstrapAuthSettingsURL,</span>
<span id="L221"><span class="lineNum">     221</span>              :       params: params,</span>
<span id="L222"><span class="lineNum">     222</span>              :       mockConfig: mockConfig,</span>
<span id="L223"><span class="lineNum">     223</span>              :     );</span>
<span id="L224"><span class="lineNum">     224</span>              : </span>
<span id="L225"><span class="lineNum">     225</span> <span class="tlaUNC">           0 :     final DOPNativeBootstrapAuthSettingsEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L226"><span class="lineNum">     226</span> <span class="tlaUNC">           0 :           () =&gt; DOPNativeBootstrapAuthSettingsEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L227"><span class="lineNum">     227</span>              :           originalData: baseResponse,</span>
<span id="L228"><span class="lineNum">     228</span>              :         ) ??</span>
<span id="L229"><span class="lineNum">     229</span> <span class="tlaUNC">           0 :         DOPNativeBootstrapAuthSettingsEntity.unserializable();</span></span>
<span id="L230"><span class="lineNum">     230</span>              :     return entity;</span>
<span id="L231"><span class="lineNum">     231</span>              :   }</span>
<span id="L232"><span class="lineNum">     232</span>              : </span>
<span id="L233"><span class="lineNum">     233</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L234"><span class="lineNum">     234</span>              :   Future&lt;DOPVerifyOTPEntity&gt; verifyOTP({</span>
<span id="L235"><span class="lineNum">     235</span>              :     required String? token,</span>
<span id="L236"><span class="lineNum">     236</span>              :     required String? otp,</span>
<span id="L237"><span class="lineNum">     237</span>              :     MockConfig? mockConfig,</span>
<span id="L238"><span class="lineNum">     238</span>              :   }) async {</span>
<span id="L239"><span class="lineNum">     239</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L240"><span class="lineNum">     240</span>              :       verifyOTPURL,</span>
<span id="L241"><span class="lineNum">     241</span> <span class="tlaUNC">           0 :       data: &lt;String, dynamic&gt;{</span></span>
<span id="L242"><span class="lineNum">     242</span>              :         'token': token,</span>
<span id="L243"><span class="lineNum">     243</span>              :         'otp': otp,</span>
<span id="L244"><span class="lineNum">     244</span>              :       },</span>
<span id="L245"><span class="lineNum">     245</span>              :       mockConfig: mockConfig,</span>
<span id="L246"><span class="lineNum">     246</span>              :     );</span>
<span id="L247"><span class="lineNum">     247</span>              : </span>
<span id="L248"><span class="lineNum">     248</span> <span class="tlaUNC">           0 :     final DOPVerifyOTPEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L249"><span class="lineNum">     249</span> <span class="tlaUNC">           0 :           () =&gt; DOPVerifyOTPEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L250"><span class="lineNum">     250</span>              :           originalData: baseResponse,</span>
<span id="L251"><span class="lineNum">     251</span>              :         ) ??</span>
<span id="L252"><span class="lineNum">     252</span> <span class="tlaUNC">           0 :         DOPVerifyOTPEntity.unserializable();</span></span>
<span id="L253"><span class="lineNum">     253</span>              :     return entity;</span>
<span id="L254"><span class="lineNum">     254</span>              :   }</span>
<span id="L255"><span class="lineNum">     255</span>              : </span>
<span id="L256"><span class="lineNum">     256</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L257"><span class="lineNum">     257</span>              :   Future&lt;DOPNativeApplicationStateEntity&gt; getApplicationState({</span>
<span id="L258"><span class="lineNum">     258</span>              :     String? token,</span>
<span id="L259"><span class="lineNum">     259</span>              :     int? flowSelectedAt,</span>
<span id="L260"><span class="lineNum">     260</span>              :     MockConfig? mockConfig,</span>
<span id="L261"><span class="lineNum">     261</span>              :   }) async {</span>
<span id="L262"><span class="lineNum">     262</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; params = &lt;String, dynamic&gt;{</span></span>
<span id="L263"><span class="lineNum">     263</span>              :       'token': token,</span>
<span id="L264"><span class="lineNum">     264</span>              :     };</span>
<span id="L265"><span class="lineNum">     265</span>              : </span>
<span id="L266"><span class="lineNum">     266</span>              :     if (flowSelectedAt != null) {</span>
<span id="L267"><span class="lineNum">     267</span> <span class="tlaUNC">           0 :       params['flow_selected_at'] = flowSelectedAt;</span></span>
<span id="L268"><span class="lineNum">     268</span>              :     }</span>
<span id="L269"><span class="lineNum">     269</span>              : </span>
<span id="L270"><span class="lineNum">     270</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L271"><span class="lineNum">     271</span>              :       getApplicationStateURL,</span>
<span id="L272"><span class="lineNum">     272</span>              :       params: params,</span>
<span id="L273"><span class="lineNum">     273</span>              :       mockConfig: mockConfig,</span>
<span id="L274"><span class="lineNum">     274</span>              :     );</span>
<span id="L275"><span class="lineNum">     275</span>              : </span>
<span id="L276"><span class="lineNum">     276</span> <span class="tlaUNC">           0 :     final DOPNativeApplicationStateEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L277"><span class="lineNum">     277</span> <span class="tlaUNC">           0 :           () =&gt; DOPNativeApplicationStateEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L278"><span class="lineNum">     278</span>              :           originalData: baseResponse,</span>
<span id="L279"><span class="lineNum">     279</span>              :         ) ??</span>
<span id="L280"><span class="lineNum">     280</span> <span class="tlaUNC">           0 :         DOPNativeApplicationStateEntity.unserializable();</span></span>
<span id="L281"><span class="lineNum">     281</span>              : </span>
<span id="L282"><span class="lineNum">     282</span>              :     return entity;</span>
<span id="L283"><span class="lineNum">     283</span>              :   }</span>
<span id="L284"><span class="lineNum">     284</span>              : </span>
<span id="L285"><span class="lineNum">     285</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L286"><span class="lineNum">     286</span>              :   Future&lt;DOPNativeMetadataEntity&gt; getMetadata({</span>
<span id="L287"><span class="lineNum">     287</span>              :     required MetadataType type,</span>
<span id="L288"><span class="lineNum">     288</span>              :     String? parentCode,</span>
<span id="L289"><span class="lineNum">     289</span>              :     MetadataType? parentType,</span>
<span id="L290"><span class="lineNum">     290</span>              :     String? metadataCode,</span>
<span id="L291"><span class="lineNum">     291</span>              :     MockConfig? mockConfig,</span>
<span id="L292"><span class="lineNum">     292</span>              :   }) async {</span>
<span id="L293"><span class="lineNum">     293</span> <span class="tlaUNC">           0 :     final Map&lt;String, String&gt; params = &lt;String, String&gt;{</span></span>
<span id="L294"><span class="lineNum">     294</span> <span class="tlaUNC">           0 :       'type': type.value,</span></span>
<span id="L295"><span class="lineNum">     295</span>              :     };</span>
<span id="L296"><span class="lineNum">     296</span>              : </span>
<span id="L297"><span class="lineNum">     297</span>              :     if (parentCode != null) {</span>
<span id="L298"><span class="lineNum">     298</span> <span class="tlaUNC">           0 :       params['parent_code'] = parentCode;</span></span>
<span id="L299"><span class="lineNum">     299</span>              :     }</span>
<span id="L300"><span class="lineNum">     300</span>              : </span>
<span id="L301"><span class="lineNum">     301</span>              :     if (parentType != null) {</span>
<span id="L302"><span class="lineNum">     302</span> <span class="tlaUNC">           0 :       params['parent_type'] = parentType.value;</span></span>
<span id="L303"><span class="lineNum">     303</span>              :     }</span>
<span id="L304"><span class="lineNum">     304</span>              : </span>
<span id="L305"><span class="lineNum">     305</span>              :     if (metadataCode != null) {</span>
<span id="L306"><span class="lineNum">     306</span> <span class="tlaUNC">           0 :       params['metadata_code'] = metadataCode;</span></span>
<span id="L307"><span class="lineNum">     307</span>              :     }</span>
<span id="L308"><span class="lineNum">     308</span>              : </span>
<span id="L309"><span class="lineNum">     309</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L310"><span class="lineNum">     310</span>              :       metadataURL,</span>
<span id="L311"><span class="lineNum">     311</span>              :       params: params,</span>
<span id="L312"><span class="lineNum">     312</span>              :       mockConfig: mockConfig,</span>
<span id="L313"><span class="lineNum">     313</span>              :     );</span>
<span id="L314"><span class="lineNum">     314</span>              : </span>
<span id="L315"><span class="lineNum">     315</span> <span class="tlaUNC">           0 :     final DOPNativeMetadataEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L316"><span class="lineNum">     316</span> <span class="tlaUNC">           0 :             () =&gt; DOPNativeMetadataEntity.fromJson(baseResponse),</span></span>
<span id="L317"><span class="lineNum">     317</span>              :             originalData: baseResponse) ??</span>
<span id="L318"><span class="lineNum">     318</span> <span class="tlaUNC">           0 :         DOPNativeMetadataEntity.unserializable();</span></span>
<span id="L319"><span class="lineNum">     319</span>              : </span>
<span id="L320"><span class="lineNum">     320</span>              :     return entity;</span>
<span id="L321"><span class="lineNum">     321</span>              :   }</span>
<span id="L322"><span class="lineNum">     322</span>              : </span>
<span id="L323"><span class="lineNum">     323</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L324"><span class="lineNum">     324</span>              :   Future&lt;DOPNativeMetadataSuggestionEntity&gt; getMetadataSuggestion({</span>
<span id="L325"><span class="lineNum">     325</span>              :     required MetadataSuggestionType type,</span>
<span id="L326"><span class="lineNum">     326</span>              :     required String searchPrefix,</span>
<span id="L327"><span class="lineNum">     327</span>              :     int? pageSize,</span>
<span id="L328"><span class="lineNum">     328</span>              :     MockConfig? mockConfig,</span>
<span id="L329"><span class="lineNum">     329</span>              :   }) async {</span>
<span id="L330"><span class="lineNum">     330</span> <span class="tlaUNC">           0 :     final Map&lt;String, String&gt; params = &lt;String, String&gt;{</span></span>
<span id="L331"><span class="lineNum">     331</span> <span class="tlaUNC">           0 :       'type': type.value,</span></span>
<span id="L332"><span class="lineNum">     332</span>              :       'search_prefix': searchPrefix,</span>
<span id="L333"><span class="lineNum">     333</span>              :     };</span>
<span id="L334"><span class="lineNum">     334</span>              : </span>
<span id="L335"><span class="lineNum">     335</span>              :     if (pageSize != null) {</span>
<span id="L336"><span class="lineNum">     336</span> <span class="tlaUNC">           0 :       params['page_size'] = pageSize.toString();</span></span>
<span id="L337"><span class="lineNum">     337</span>              :     }</span>
<span id="L338"><span class="lineNum">     338</span>              : </span>
<span id="L339"><span class="lineNum">     339</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L340"><span class="lineNum">     340</span>              :       metadataSuggestionURL,</span>
<span id="L341"><span class="lineNum">     341</span>              :       params: params,</span>
<span id="L342"><span class="lineNum">     342</span>              :       mockConfig: mockConfig,</span>
<span id="L343"><span class="lineNum">     343</span>              :     );</span>
<span id="L344"><span class="lineNum">     344</span>              : </span>
<span id="L345"><span class="lineNum">     345</span> <span class="tlaUNC">           0 :     final DOPNativeMetadataSuggestionEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L346"><span class="lineNum">     346</span> <span class="tlaUNC">           0 :             () =&gt; DOPNativeMetadataSuggestionEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L347"><span class="lineNum">     347</span>              :             originalData: baseResponse) ??</span>
<span id="L348"><span class="lineNum">     348</span> <span class="tlaUNC">           0 :         DOPNativeMetadataSuggestionEntity.unserializable();</span></span>
<span id="L349"><span class="lineNum">     349</span>              : </span>
<span id="L350"><span class="lineNum">     350</span>              :     return entity;</span>
<span id="L351"><span class="lineNum">     351</span>              :   }</span>
<span id="L352"><span class="lineNum">     352</span>              : </span>
<span id="L353"><span class="lineNum">     353</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L354"><span class="lineNum">     354</span>              :   Future&lt;DOPNativeAppraisingStatusEntity&gt; getAppraisingStatus({MockConfig? mockConfig}) async {</span>
<span id="L355"><span class="lineNum">     355</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L356"><span class="lineNum">     356</span>              :       appraisingStatusURL,</span>
<span id="L357"><span class="lineNum">     357</span>              :       mockConfig: mockConfig,</span>
<span id="L358"><span class="lineNum">     358</span>              :     );</span>
<span id="L359"><span class="lineNum">     359</span>              : </span>
<span id="L360"><span class="lineNum">     360</span> <span class="tlaUNC">           0 :     final DOPNativeAppraisingStatusEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L361"><span class="lineNum">     361</span> <span class="tlaUNC">           0 :             () =&gt; DOPNativeAppraisingStatusEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L362"><span class="lineNum">     362</span>              :             originalData: baseResponse) ??</span>
<span id="L363"><span class="lineNum">     363</span> <span class="tlaUNC">           0 :         DOPNativeAppraisingStatusEntity.unserializable();</span></span>
<span id="L364"><span class="lineNum">     364</span>              : </span>
<span id="L365"><span class="lineNum">     365</span>              :     return entity;</span>
<span id="L366"><span class="lineNum">     366</span>              :   }</span>
<span id="L367"><span class="lineNum">     367</span>              : </span>
<span id="L368"><span class="lineNum">     368</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L369"><span class="lineNum">     369</span>              :   Future&lt;DOPNativeCreditAssignmentEntity&gt; getAppraisingCreditAssignment(</span>
<span id="L370"><span class="lineNum">     370</span>              :       {MockConfig? mockConfig}) async {</span>
<span id="L371"><span class="lineNum">     371</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L372"><span class="lineNum">     372</span>              :       appraisingCreditAssignment,</span>
<span id="L373"><span class="lineNum">     373</span>              :       mockConfig: mockConfig,</span>
<span id="L374"><span class="lineNum">     374</span>              :     );</span>
<span id="L375"><span class="lineNum">     375</span>              : </span>
<span id="L376"><span class="lineNum">     376</span> <span class="tlaUNC">           0 :     final DOPNativeCreditAssignmentEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L377"><span class="lineNum">     377</span> <span class="tlaUNC">           0 :             () =&gt; DOPNativeCreditAssignmentEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L378"><span class="lineNum">     378</span>              :             originalData: baseResponse) ??</span>
<span id="L379"><span class="lineNum">     379</span> <span class="tlaUNC">           0 :         DOPNativeCreditAssignmentEntity.unserializable();</span></span>
<span id="L380"><span class="lineNum">     380</span>              : </span>
<span id="L381"><span class="lineNum">     381</span>              :     return entity;</span>
<span id="L382"><span class="lineNum">     382</span>              :   }</span>
<span id="L383"><span class="lineNum">     383</span>              : </span>
<span id="L384"><span class="lineNum">     384</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L385"><span class="lineNum">     385</span>              :   Future&lt;BaseEntity&gt; submitApplicationForm({</span>
<span id="L386"><span class="lineNum">     386</span>              :     required DOPNativeApplicationSubmitFormRequest form,</span>
<span id="L387"><span class="lineNum">     387</span>              :     MockConfig? mockConfig,</span>
<span id="L388"><span class="lineNum">     388</span>              :   }) async {</span>
<span id="L389"><span class="lineNum">     389</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L390"><span class="lineNum">     390</span>              :       submitFormApplicationUrl,</span>
<span id="L391"><span class="lineNum">     391</span> <span class="tlaUNC">           0 :       data: form.toJson(),</span></span>
<span id="L392"><span class="lineNum">     392</span>              :       mockConfig: mockConfig,</span>
<span id="L393"><span class="lineNum">     393</span>              :     );</span>
<span id="L394"><span class="lineNum">     394</span>              : </span>
<span id="L395"><span class="lineNum">     395</span> <span class="tlaUNC">           0 :     return BaseEntity.fromBaseResponse(baseResponse);</span></span>
<span id="L396"><span class="lineNum">     396</span>              :   }</span>
<span id="L397"><span class="lineNum">     397</span>              : </span>
<span id="L398"><span class="lineNum">     398</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L399"><span class="lineNum">     399</span>              :   Future&lt;DOPNativeApplicationFormDataEntity&gt; getApplicationFormData(</span>
<span id="L400"><span class="lineNum">     400</span>              :       {MockConfig? mockConfig}) async {</span>
<span id="L401"><span class="lineNum">     401</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L402"><span class="lineNum">     402</span>              :       applicationFormDataURL,</span>
<span id="L403"><span class="lineNum">     403</span>              :       mockConfig: mockConfig,</span>
<span id="L404"><span class="lineNum">     404</span>              :     );</span>
<span id="L405"><span class="lineNum">     405</span>              : </span>
<span id="L406"><span class="lineNum">     406</span> <span class="tlaUNC">           0 :     final DOPNativeApplicationFormDataEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L407"><span class="lineNum">     407</span> <span class="tlaUNC">           0 :           () =&gt; DOPNativeApplicationFormDataEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L408"><span class="lineNum">     408</span>              :           originalData: baseResponse,</span>
<span id="L409"><span class="lineNum">     409</span>              :         ) ??</span>
<span id="L410"><span class="lineNum">     410</span> <span class="tlaUNC">           0 :         DOPNativeApplicationFormDataEntity.unserializable();</span></span>
<span id="L411"><span class="lineNum">     411</span>              : </span>
<span id="L412"><span class="lineNum">     412</span>              :     return entity;</span>
<span id="L413"><span class="lineNum">     413</span>              :   }</span>
<span id="L414"><span class="lineNum">     414</span>              : </span>
<span id="L415"><span class="lineNum">     415</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L416"><span class="lineNum">     416</span>              :   Future&lt;BaseEntity&gt; confirmCif({required bool useNewCif, MockConfig? mockConfig}) async {</span>
<span id="L417"><span class="lineNum">     417</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; body = &lt;String, dynamic&gt;{</span></span>
<span id="L418"><span class="lineNum">     418</span>              :       'use_new_cif': useNewCif,</span>
<span id="L419"><span class="lineNum">     419</span>              :     };</span>
<span id="L420"><span class="lineNum">     420</span>              : </span>
<span id="L421"><span class="lineNum">     421</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L422"><span class="lineNum">     422</span>              :       cifConfirmUrl,</span>
<span id="L423"><span class="lineNum">     423</span>              :       data: body,</span>
<span id="L424"><span class="lineNum">     424</span>              :       mockConfig: mockConfig,</span>
<span id="L425"><span class="lineNum">     425</span>              :     );</span>
<span id="L426"><span class="lineNum">     426</span>              : </span>
<span id="L427"><span class="lineNum">     427</span> <span class="tlaUNC">           0 :     return BaseEntity.fromBaseResponse(baseResponse);</span></span>
<span id="L428"><span class="lineNum">     428</span>              :   }</span>
<span id="L429"><span class="lineNum">     429</span>              : </span>
<span id="L430"><span class="lineNum">     430</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L431"><span class="lineNum">     431</span>              :   Future&lt;BaseEntity&gt; getApplicationNextState({MockConfig? mockConfig}) async {</span>
<span id="L432"><span class="lineNum">     432</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; body = &lt;String, dynamic&gt;{</span></span>
<span id="L433"><span class="lineNum">     433</span>              :       'action': 'evo_next',</span>
<span id="L434"><span class="lineNum">     434</span>              :     };</span>
<span id="L435"><span class="lineNum">     435</span>              : </span>
<span id="L436"><span class="lineNum">     436</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L437"><span class="lineNum">     437</span>              :       getApplicationNextStateUrl,</span>
<span id="L438"><span class="lineNum">     438</span>              :       data: body,</span>
<span id="L439"><span class="lineNum">     439</span>              :       mockConfig: mockConfig,</span>
<span id="L440"><span class="lineNum">     440</span>              :     );</span>
<span id="L441"><span class="lineNum">     441</span>              : </span>
<span id="L442"><span class="lineNum">     442</span> <span class="tlaUNC">           0 :     return BaseEntity.fromBaseResponse(baseResponse);</span></span>
<span id="L443"><span class="lineNum">     443</span>              :   }</span>
<span id="L444"><span class="lineNum">     444</span>              : </span>
<span id="L445"><span class="lineNum">     445</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L446"><span class="lineNum">     446</span>              :   Future&lt;ESignStateEntity&gt; getESignState({MockConfig? mockConfig}) async {</span>
<span id="L447"><span class="lineNum">     447</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L448"><span class="lineNum">     448</span>              :       getESignStateUrl,</span>
<span id="L449"><span class="lineNum">     449</span>              :       mockConfig: mockConfig,</span>
<span id="L450"><span class="lineNum">     450</span>              :     );</span>
<span id="L451"><span class="lineNum">     451</span>              : </span>
<span id="L452"><span class="lineNum">     452</span> <span class="tlaUNC">           0 :     final ESignStateEntity entity = commonUtilFunction.serialize(</span></span>
<span id="L453"><span class="lineNum">     453</span> <span class="tlaUNC">           0 :           () =&gt; ESignStateEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L454"><span class="lineNum">     454</span>              :           originalData: baseResponse,</span>
<span id="L455"><span class="lineNum">     455</span>              :         ) ??</span>
<span id="L456"><span class="lineNum">     456</span> <span class="tlaUNC">           0 :         ESignStateEntity.unserializable();</span></span>
<span id="L457"><span class="lineNum">     457</span>              : </span>
<span id="L458"><span class="lineNum">     458</span>              :     return entity;</span>
<span id="L459"><span class="lineNum">     459</span>              :   }</span>
<span id="L460"><span class="lineNum">     460</span>              : </span>
<span id="L461"><span class="lineNum">     461</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L462"><span class="lineNum">     462</span>              :   Future&lt;DOPNativeCardStatusEntity&gt; getCardStatus({MockConfig? mockConfig}) async {</span>
<span id="L463"><span class="lineNum">     463</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get(</span></span>
<span id="L464"><span class="lineNum">     464</span>              :       cardStatusUrl,</span>
<span id="L465"><span class="lineNum">     465</span>              :       mockConfig: mockConfig,</span>
<span id="L466"><span class="lineNum">     466</span>              :     );</span>
<span id="L467"><span class="lineNum">     467</span>              : </span>
<span id="L468"><span class="lineNum">     468</span> <span class="tlaUNC">           0 :     return commonUtilFunction.serialize(</span></span>
<span id="L469"><span class="lineNum">     469</span> <span class="tlaUNC">           0 :             () =&gt; DOPNativeCardStatusEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L470"><span class="lineNum">     470</span>              :             originalData: baseResponse) ??</span>
<span id="L471"><span class="lineNum">     471</span> <span class="tlaUNC">           0 :         DOPNativeCardStatusEntity.unserializable();</span></span>
<span id="L472"><span class="lineNum">     472</span>              :   }</span>
<span id="L473"><span class="lineNum">     473</span>              : </span>
<span id="L474"><span class="lineNum">     474</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L475"><span class="lineNum">     475</span>              :   Future&lt;DOPNativeActivateCardEntity&gt; activateCard({</span>
<span id="L476"><span class="lineNum">     476</span>              :     required int posLimit,</span>
<span id="L477"><span class="lineNum">     477</span>              :     MockConfig? mockConfig,</span>
<span id="L478"><span class="lineNum">     478</span>              :   }) async {</span>
<span id="L479"><span class="lineNum">     479</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L480"><span class="lineNum">     480</span>              :       activateCardUrl,</span>
<span id="L481"><span class="lineNum">     481</span> <span class="tlaUNC">           0 :       data: &lt;String, dynamic&gt;{</span></span>
<span id="L482"><span class="lineNum">     482</span>              :         'pos_limit': posLimit,</span>
<span id="L483"><span class="lineNum">     483</span>              :       },</span>
<span id="L484"><span class="lineNum">     484</span>              :       mockConfig: mockConfig,</span>
<span id="L485"><span class="lineNum">     485</span>              :     );</span>
<span id="L486"><span class="lineNum">     486</span>              : </span>
<span id="L487"><span class="lineNum">     487</span> <span class="tlaUNC">           0 :     return commonUtilFunction.serialize(</span></span>
<span id="L488"><span class="lineNum">     488</span> <span class="tlaUNC">           0 :             () =&gt; DOPNativeActivateCardEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L489"><span class="lineNum">     489</span>              :             originalData: baseResponse) ??</span>
<span id="L490"><span class="lineNum">     490</span> <span class="tlaUNC">           0 :         DOPNativeActivateCardEntity.unserializable();</span></span>
<span id="L491"><span class="lineNum">     491</span>              :   }</span>
<span id="L492"><span class="lineNum">     492</span>              : </span>
<span id="L493"><span class="lineNum">     493</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L494"><span class="lineNum">     494</span>              :   Future&lt;DOPNativeESignPrepareEntity&gt; prepareESign({MockConfig? mockConfig}) async {</span>
<span id="L495"><span class="lineNum">     495</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L496"><span class="lineNum">     496</span>              :       eSignPrepareUrl,</span>
<span id="L497"><span class="lineNum">     497</span>              :       mockConfig: mockConfig,</span>
<span id="L498"><span class="lineNum">     498</span>              :     );</span>
<span id="L499"><span class="lineNum">     499</span>              : </span>
<span id="L500"><span class="lineNum">     500</span> <span class="tlaUNC">           0 :     return commonUtilFunction.serialize(</span></span>
<span id="L501"><span class="lineNum">     501</span> <span class="tlaUNC">           0 :             () =&gt; DOPNativeESignPrepareEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L502"><span class="lineNum">     502</span>              :             originalData: baseResponse) ??</span>
<span id="L503"><span class="lineNum">     503</span> <span class="tlaUNC">           0 :         DOPNativeESignPrepareEntity.unserializable();</span></span>
<span id="L504"><span class="lineNum">     504</span>              :   }</span>
<span id="L505"><span class="lineNum">     505</span>              : </span>
<span id="L506"><span class="lineNum">     506</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L507"><span class="lineNum">     507</span>              :   Future&lt;BaseEntity&gt; submitESign(</span>
<span id="L508"><span class="lineNum">     508</span>              :       {required DOPNativeESignFormRequest form, MockConfig? mockConfig}) async {</span>
<span id="L509"><span class="lineNum">     509</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L510"><span class="lineNum">     510</span>              :       submitESignUrl,</span>
<span id="L511"><span class="lineNum">     511</span> <span class="tlaUNC">           0 :       data: form.toJson(),</span></span>
<span id="L512"><span class="lineNum">     512</span>              :       mockConfig: mockConfig,</span>
<span id="L513"><span class="lineNum">     513</span>              :     );</span>
<span id="L514"><span class="lineNum">     514</span>              : </span>
<span id="L515"><span class="lineNum">     515</span> <span class="tlaUNC">           0 :     return BaseEntity.fromBaseResponse(baseResponse);</span></span>
<span id="L516"><span class="lineNum">     516</span>              :   }</span>
<span id="L517"><span class="lineNum">     517</span>              : </span>
<span id="L518"><span class="lineNum">     518</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L519"><span class="lineNum">     519</span>              :   Future&lt;BaseEntity&gt; submitESignIntroNext({MockConfig? mockConfig}) async {</span>
<span id="L520"><span class="lineNum">     520</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L521"><span class="lineNum">     521</span>              :       submitESignIntroNextUrl,</span>
<span id="L522"><span class="lineNum">     522</span>              :       mockConfig: mockConfig,</span>
<span id="L523"><span class="lineNum">     523</span>              :     );</span>
<span id="L524"><span class="lineNum">     524</span>              : </span>
<span id="L525"><span class="lineNum">     525</span> <span class="tlaUNC">           0 :     return BaseEntity.fromBaseResponse(baseResponse);</span></span>
<span id="L526"><span class="lineNum">     526</span>              :   }</span>
<span id="L527"><span class="lineNum">     527</span>              : </span>
<span id="L528"><span class="lineNum">     528</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L529"><span class="lineNum">     529</span>              :   Future&lt;BaseEntity&gt; logEvent({</span>
<span id="L530"><span class="lineNum">     530</span>              :     required DOPNativeLogEventDataRequest data,</span>
<span id="L531"><span class="lineNum">     531</span>              :     MockConfig? mockConfig,</span>
<span id="L532"><span class="lineNum">     532</span>              :   }) async {</span>
<span id="L533"><span class="lineNum">     533</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L534"><span class="lineNum">     534</span>              :       logEventUrl,</span>
<span id="L535"><span class="lineNum">     535</span> <span class="tlaUNC">           0 :       data: data.toJson(),</span></span>
<span id="L536"><span class="lineNum">     536</span>              :       mockConfig: mockConfig,</span>
<span id="L537"><span class="lineNum">     537</span>              :     );</span>
<span id="L538"><span class="lineNum">     538</span>              : </span>
<span id="L539"><span class="lineNum">     539</span> <span class="tlaUNC">           0 :     return BaseEntity.fromBaseResponse(baseResponse);</span></span>
<span id="L540"><span class="lineNum">     540</span>              :   }</span>
<span id="L541"><span class="lineNum">     541</span>              : </span>
<span id="L542"><span class="lineNum">     542</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L543"><span class="lineNum">     543</span>              :   Future&lt;BaseEntity&gt; collectLocation({</span>
<span id="L544"><span class="lineNum">     544</span>              :     required DOPNativeCollectLocationRequest data,</span>
<span id="L545"><span class="lineNum">     545</span>              :     MockConfig? mockConfig,</span>
<span id="L546"><span class="lineNum">     546</span>              :   }) async {</span>
<span id="L547"><span class="lineNum">     547</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L548"><span class="lineNum">     548</span>              :       collectLocationUrl,</span>
<span id="L549"><span class="lineNum">     549</span> <span class="tlaUNC">           0 :       data: data.toJson(),</span></span>
<span id="L550"><span class="lineNum">     550</span>              :       mockConfig: mockConfig,</span>
<span id="L551"><span class="lineNum">     551</span>              :     );</span>
<span id="L552"><span class="lineNum">     552</span>              : </span>
<span id="L553"><span class="lineNum">     553</span> <span class="tlaUNC">           0 :     return BaseEntity.fromBaseResponse(baseResponse);</span></span>
<span id="L554"><span class="lineNum">     554</span>              :   }</span>
<span id="L555"><span class="lineNum">     555</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
