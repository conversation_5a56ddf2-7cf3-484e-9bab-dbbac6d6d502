<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/repository/checkout_repo_impl.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/repository">lib/data/repository</a> - checkout_repo_impl.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">77</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import 'package:flutter_common_package/data/http_client/base_response.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import 'package:flutter_common_package/data/http_client/common_request_option.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import 'package:flutter_common_package/data/http_client/mock_config.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import 'package:flutter_common_package/util/utils.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../feature/biometric_pin_confirm/biometric_pin_data.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : import '../constants.dart';</span>
<span id="L8"><span class="lineNum">       8</span>              : import '../request/order_flow_type.dart';</span>
<span id="L9"><span class="lineNum">       9</span>              : import '../request/transaction_history_request.dart';</span>
<span id="L10"><span class="lineNum">      10</span>              : import '../request/transaction_history_request_v2.dart';</span>
<span id="L11"><span class="lineNum">      11</span>              : import '../response/cancel_transaction_entity.dart';</span>
<span id="L12"><span class="lineNum">      12</span>              : import '../response/clone_order_entity.dart';</span>
<span id="L13"><span class="lineNum">      13</span>              : import '../response/confirm_and_pay_order_entity.dart';</span>
<span id="L14"><span class="lineNum">      14</span>              : import '../response/create_order_entity.dart';</span>
<span id="L15"><span class="lineNum">      15</span>              : import '../response/payment_result_entity.dart';</span>
<span id="L16"><span class="lineNum">      16</span>              : import '../response/transaction_history_entity.dart';</span>
<span id="L17"><span class="lineNum">      17</span>              : import '../response/update_order_entity.dart';</span>
<span id="L18"><span class="lineNum">      18</span>              : import '../response/vn_pay_qr_info_entity.dart';</span>
<span id="L19"><span class="lineNum">      19</span>              : import 'base_repo.dart';</span>
<span id="L20"><span class="lineNum">      20</span>              : import 'checkout_repo.dart';</span>
<span id="L21"><span class="lineNum">      21</span>              : </span>
<span id="L22"><span class="lineNum">      22</span>              : class CheckoutRepoImpl extends BaseRepo implements CheckOutRepo {</span>
<span id="L23"><span class="lineNum">      23</span>              :   // API urls</span>
<span id="L24"><span class="lineNum">      24</span>              :   static const String checkOutUrl = 'checkout';</span>
<span id="L25"><span class="lineNum">      25</span>              :   static const String confirmAndPayUrl = '$checkOutUrl/{id}/confirm-and-pay';</span>
<span id="L26"><span class="lineNum">      26</span>              :   static const String transactionUrl = 'transactions';</span>
<span id="L27"><span class="lineNum">      27</span>              :   static const String transactionV2Url = 'v2/transactions';</span>
<span id="L28"><span class="lineNum">      28</span>              :   static const String cancelTransactionUrl = '$transactionUrl/{id}/cancel';</span>
<span id="L29"><span class="lineNum">      29</span>              : </span>
<span id="L30"><span class="lineNum">      30</span> <span class="tlaUNC">           0 :   static String cloneOrderUrl(String? id) =&gt; '$checkOutUrl/$id/clone';</span></span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :   CheckoutRepoImpl(super.client);</span></span>
<span id="L33"><span class="lineNum">      33</span>              : </span>
<span id="L34"><span class="lineNum">      34</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L35"><span class="lineNum">      35</span>              :   Future&lt;CreateOrderEntity&gt; createOrder({</span>
<span id="L36"><span class="lineNum">      36</span>              :     required String? storeId,</span>
<span id="L37"><span class="lineNum">      37</span>              :     required String? productCode,</span>
<span id="L38"><span class="lineNum">      38</span>              :     required int? amount,</span>
<span id="L39"><span class="lineNum">      39</span>              :     required String? desc,</span>
<span id="L40"><span class="lineNum">      40</span>              :     required VNPayQrInfoEntity? vnPayQrInfo,</span>
<span id="L41"><span class="lineNum">      41</span>              :     PaymentService paymentService = PaymentService.outrightPurchase,</span>
<span id="L42"><span class="lineNum">      42</span>              :     MockConfig? mockConfig,</span>
<span id="L43"><span class="lineNum">      43</span>              :   }) async {</span>
<span id="L44"><span class="lineNum">      44</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; data = &lt;String, dynamic&gt;{</span></span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :       'store_id': storeId,</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :       'product_code': productCode,</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :       'payment_service': paymentService.value,</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :       if (vnPayQrInfo != null) 'vnpay_qr_info': vnPayQrInfo.toJson(),</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :       if (amount != null) 'order_amount': amount,</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :       if (desc != null) 'description': desc,</span></span>
<span id="L51"><span class="lineNum">      51</span>              :     };</span>
<span id="L52"><span class="lineNum">      52</span>              : </span>
<span id="L53"><span class="lineNum">      53</span>              :     final BaseResponse baseResponse =</span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :         await client.post(checkOutUrl, data: data, mockConfig: mockConfig);</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :     final CreateOrderEntity createOrderEntity = commonUtilFunction.serialize(</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :             () =&gt; CreateOrderEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L57"><span class="lineNum">      57</span>              :             originalData: baseResponse) ??</span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :         CreateOrderEntity.unserializable();</span></span>
<span id="L59"><span class="lineNum">      59</span>              :     return createOrderEntity;</span>
<span id="L60"><span class="lineNum">      60</span>              :   }</span>
<span id="L61"><span class="lineNum">      61</span>              : </span>
<span id="L62"><span class="lineNum">      62</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L63"><span class="lineNum">      63</span>              :   Future&lt;CreateOrderEntity&gt; createOrderByOrderId({</span>
<span id="L64"><span class="lineNum">      64</span>              :     required String? storeId,</span>
<span id="L65"><span class="lineNum">      65</span>              :     required String? productCode,</span>
<span id="L66"><span class="lineNum">      66</span>              :     required String? merchantId,</span>
<span id="L67"><span class="lineNum">      67</span>              :     required String? orderId,</span>
<span id="L68"><span class="lineNum">      68</span>              :     MockConfig? mockConfig,</span>
<span id="L69"><span class="lineNum">      69</span>              :   }) async {</span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; data = &lt;String, dynamic&gt;{</span></span>
<span id="L71"><span class="lineNum">      71</span>              :       'store_id': storeId,</span>
<span id="L72"><span class="lineNum">      72</span>              :       'order_id': orderId,</span>
<span id="L73"><span class="lineNum">      73</span>              :       'merchant_id': merchantId,</span>
<span id="L74"><span class="lineNum">      74</span>              :       'product_code': productCode,</span>
<span id="L75"><span class="lineNum">      75</span>              :     };</span>
<span id="L76"><span class="lineNum">      76</span>              : </span>
<span id="L77"><span class="lineNum">      77</span>              :     final BaseResponse baseResponse =</span>
<span id="L78"><span class="lineNum">      78</span> <span class="tlaUNC">           0 :         await client.post(checkOutUrl, data: data, mockConfig: mockConfig);</span></span>
<span id="L79"><span class="lineNum">      79</span> <span class="tlaUNC">           0 :     final CreateOrderEntity createOrderEntity = commonUtilFunction.serialize(</span></span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :             () =&gt; CreateOrderEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L81"><span class="lineNum">      81</span>              :             originalData: baseResponse) ??</span>
<span id="L82"><span class="lineNum">      82</span> <span class="tlaUNC">           0 :         CreateOrderEntity.unserializable();</span></span>
<span id="L83"><span class="lineNum">      83</span>              :     return createOrderEntity;</span>
<span id="L84"><span class="lineNum">      84</span>              :   }</span>
<span id="L85"><span class="lineNum">      85</span>              : </span>
<span id="L86"><span class="lineNum">      86</span>              :   /// [mockVerdict] only supports:</span>
<span id="L87"><span class="lineNum">      87</span>              :   /// [ConfirmAndPayOrderEntity.verdictMissingPaymentMethod]</span>
<span id="L88"><span class="lineNum">      88</span>              :   /// [ConfirmAndPayOrderEntity.verdictPromotionInvalid]</span>
<span id="L89"><span class="lineNum">      89</span>              :   /// [ConfirmAndPayOrderEntity.verdictPromotionExpired]</span>
<span id="L90"><span class="lineNum">      90</span>              :   /// [ConfirmAndPayOrderEntity.verdictPromotionUnqualified]</span>
<span id="L91"><span class="lineNum">      91</span>              :   /// [ConfirmAndPayOrderEntity.verdictPromotionDuplicate]</span>
<span id="L92"><span class="lineNum">      92</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L93"><span class="lineNum">      93</span>              :   Future&lt;ConfirmAndPayOrderEntity&gt; confirmAndPay({</span>
<span id="L94"><span class="lineNum">      94</span>              :     required AuthenticateType authType,</span>
<span id="L95"><span class="lineNum">      95</span>              :     String? sessionId,</span>
<span id="L96"><span class="lineNum">      96</span>              :     String? idempotencyKey,</span>
<span id="L97"><span class="lineNum">      97</span>              :     int? userChargeAmount,</span>
<span id="L98"><span class="lineNum">      98</span>              :     String? biometricToken,</span>
<span id="L99"><span class="lineNum">      99</span>              :     String? paymentMethodId,</span>
<span id="L100"><span class="lineNum">     100</span>              :     String? pin,</span>
<span id="L101"><span class="lineNum">     101</span>              :     List&lt;int?&gt;? voucherIds,</span>
<span id="L102"><span class="lineNum">     102</span>              :     String? emiOfferId,</span>
<span id="L103"><span class="lineNum">     103</span>              :     MockConfig? mockConfig,</span>
<span id="L104"><span class="lineNum">     104</span>              :   }) async {</span>
<span id="L105"><span class="lineNum">     105</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; data = &lt;String, dynamic&gt;{</span></span>
<span id="L106"><span class="lineNum">     106</span>              :       'user_charge_amount': userChargeAmount,</span>
<span id="L107"><span class="lineNum">     107</span> <span class="tlaUNC">           0 :       'authenticate_type': authType.value</span></span>
<span id="L108"><span class="lineNum">     108</span>              :     };</span>
<span id="L109"><span class="lineNum">     109</span>              : </span>
<span id="L110"><span class="lineNum">     110</span> <span class="tlaUNC">           0 :     if (authType == AuthenticateType.biometricToken) {</span></span>
<span id="L111"><span class="lineNum">     111</span> <span class="tlaUNC">           0 :       data['biometric_token'] = biometricToken;</span></span>
<span id="L112"><span class="lineNum">     112</span> <span class="tlaUNC">           0 :     } else if (authType == AuthenticateType.pin) {</span></span>
<span id="L113"><span class="lineNum">     113</span> <span class="tlaUNC">           0 :       data['pin'] = pin;</span></span>
<span id="L114"><span class="lineNum">     114</span>              :     }</span>
<span id="L115"><span class="lineNum">     115</span>              : </span>
<span id="L116"><span class="lineNum">     116</span>              :     if (paymentMethodId != null) {</span>
<span id="L117"><span class="lineNum">     117</span> <span class="tlaUNC">           0 :       data['payment_method_id'] = paymentMethodId;</span></span>
<span id="L118"><span class="lineNum">     118</span>              :     }</span>
<span id="L119"><span class="lineNum">     119</span>              : </span>
<span id="L120"><span class="lineNum">     120</span> <span class="tlaUNC">           0 :     if (voucherIds?.isNotEmpty == true) {</span></span>
<span id="L121"><span class="lineNum">     121</span> <span class="tlaUNC">           0 :       data['voucher_ids'] = voucherIds;</span></span>
<span id="L122"><span class="lineNum">     122</span>              :     }</span>
<span id="L123"><span class="lineNum">     123</span>              : </span>
<span id="L124"><span class="lineNum">     124</span>              :     if (emiOfferId != null) {</span>
<span id="L125"><span class="lineNum">     125</span> <span class="tlaUNC">           0 :       data['emi_offer_id'] = emiOfferId;</span></span>
<span id="L126"><span class="lineNum">     126</span>              :     }</span>
<span id="L127"><span class="lineNum">     127</span>              : </span>
<span id="L128"><span class="lineNum">     128</span> <span class="tlaUNC">           0 :     final String confirmPayUrl = confirmAndPayUrl.replaceAll('{id}', sessionId ?? '');</span></span>
<span id="L129"><span class="lineNum">     129</span>              : </span>
<span id="L130"><span class="lineNum">     130</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.post(</span></span>
<span id="L131"><span class="lineNum">     131</span>              :       confirmPayUrl,</span>
<span id="L132"><span class="lineNum">     132</span> <span class="tlaUNC">           0 :       requestOption: CommonRequestOption(</span></span>
<span id="L133"><span class="lineNum">     133</span> <span class="tlaUNC">           0 :         headers: &lt;String, String?&gt;{HeaderKey.idempotencyKey: idempotencyKey},</span></span>
<span id="L134"><span class="lineNum">     134</span>              :       ),</span>
<span id="L135"><span class="lineNum">     135</span>              :       data: data,</span>
<span id="L136"><span class="lineNum">     136</span>              :       mockConfig: mockConfig,</span>
<span id="L137"><span class="lineNum">     137</span>              :     );</span>
<span id="L138"><span class="lineNum">     138</span>              : </span>
<span id="L139"><span class="lineNum">     139</span> <span class="tlaUNC">           0 :     final ConfirmAndPayOrderEntity confirmAndPayOrderEntity = commonUtilFunction.serialize(</span></span>
<span id="L140"><span class="lineNum">     140</span> <span class="tlaUNC">           0 :             () =&gt; ConfirmAndPayOrderEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L141"><span class="lineNum">     141</span>              :             originalData: baseResponse) ??</span>
<span id="L142"><span class="lineNum">     142</span> <span class="tlaUNC">           0 :         ConfirmAndPayOrderEntity.unserializable();</span></span>
<span id="L143"><span class="lineNum">     143</span>              : </span>
<span id="L144"><span class="lineNum">     144</span>              :     return confirmAndPayOrderEntity;</span>
<span id="L145"><span class="lineNum">     145</span>              :   }</span>
<span id="L146"><span class="lineNum">     146</span>              : </span>
<span id="L147"><span class="lineNum">     147</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L148"><span class="lineNum">     148</span>              :   Future&lt;CreateOrderEntity&gt; getCheckOutDetail(String? id, {MockConfig? mockConfig}) async {</span>
<span id="L149"><span class="lineNum">     149</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client.get('$checkOutUrl/$id', mockConfig: mockConfig);</span></span>
<span id="L150"><span class="lineNum">     150</span> <span class="tlaUNC">           0 :     final CreateOrderEntity createOrderEntity = commonUtilFunction.serialize(</span></span>
<span id="L151"><span class="lineNum">     151</span> <span class="tlaUNC">           0 :             () =&gt; CreateOrderEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L152"><span class="lineNum">     152</span>              :             originalData: baseResponse) ??</span>
<span id="L153"><span class="lineNum">     153</span> <span class="tlaUNC">           0 :         CreateOrderEntity.unserializable();</span></span>
<span id="L154"><span class="lineNum">     154</span>              :     return createOrderEntity;</span>
<span id="L155"><span class="lineNum">     155</span>              :   }</span>
<span id="L156"><span class="lineNum">     156</span>              : </span>
<span id="L157"><span class="lineNum">     157</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L158"><span class="lineNum">     158</span>              :   Future&lt;PaymentResultEntity&gt; getTransactionDetail({</span>
<span id="L159"><span class="lineNum">     159</span>              :     required String? transactionId,</span>
<span id="L160"><span class="lineNum">     160</span>              :     MockConfig? mockConfig,</span>
<span id="L161"><span class="lineNum">     161</span>              :   }) async {</span>
<span id="L162"><span class="lineNum">     162</span>              :     final BaseResponse baseResponse =</span>
<span id="L163"><span class="lineNum">     163</span> <span class="tlaUNC">           0 :         await client.get('$transactionUrl/$transactionId', mockConfig: mockConfig);</span></span>
<span id="L164"><span class="lineNum">     164</span> <span class="tlaUNC">           0 :     final PaymentResultEntity result = commonUtilFunction.serialize(</span></span>
<span id="L165"><span class="lineNum">     165</span> <span class="tlaUNC">           0 :           () =&gt; PaymentResultEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L166"><span class="lineNum">     166</span>              :           originalData: baseResponse,</span>
<span id="L167"><span class="lineNum">     167</span>              :         ) ??</span>
<span id="L168"><span class="lineNum">     168</span> <span class="tlaUNC">           0 :         PaymentResultEntity.unserializable();</span></span>
<span id="L169"><span class="lineNum">     169</span>              :     return result;</span>
<span id="L170"><span class="lineNum">     170</span>              :   }</span>
<span id="L171"><span class="lineNum">     171</span>              : </span>
<span id="L172"><span class="lineNum">     172</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L173"><span class="lineNum">     173</span>              :   Future&lt;CancelTransactionEntity&gt; cancelTransaction(String? id, {MockConfig? mockConfig}) async {</span>
<span id="L174"><span class="lineNum">     174</span> <span class="tlaUNC">           0 :     final BaseResponse baseResponse = await client</span></span>
<span id="L175"><span class="lineNum">     175</span> <span class="tlaUNC">           0 :         .post(cancelTransactionUrl.replaceAll('{id}', id ?? ''), mockConfig: mockConfig);</span></span>
<span id="L176"><span class="lineNum">     176</span> <span class="tlaUNC">           0 :     final CancelTransactionEntity cancelTransactionEntity = commonUtilFunction.serialize(</span></span>
<span id="L177"><span class="lineNum">     177</span> <span class="tlaUNC">           0 :             () =&gt; CancelTransactionEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L178"><span class="lineNum">     178</span>              :             originalData: baseResponse) ??</span>
<span id="L179"><span class="lineNum">     179</span> <span class="tlaUNC">           0 :         CancelTransactionEntity.unserializable();</span></span>
<span id="L180"><span class="lineNum">     180</span>              :     return cancelTransactionEntity;</span>
<span id="L181"><span class="lineNum">     181</span>              :   }</span>
<span id="L182"><span class="lineNum">     182</span>              : </span>
<span id="L183"><span class="lineNum">     183</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L184"><span class="lineNum">     184</span>              :   Future&lt;UpdateOrderEntity&gt; updateOrder({</span>
<span id="L185"><span class="lineNum">     185</span>              :     String? sessionId,</span>
<span id="L186"><span class="lineNum">     186</span>              :     String? paymentMethodId,</span>
<span id="L187"><span class="lineNum">     187</span>              :     List&lt;int?&gt;? voucherIds,</span>
<span id="L188"><span class="lineNum">     188</span>              :     OrderFlowType? flowType,</span>
<span id="L189"><span class="lineNum">     189</span>              :     String? emiOfferId,</span>
<span id="L190"><span class="lineNum">     190</span>              :     MockConfig? mockConfig,</span>
<span id="L191"><span class="lineNum">     191</span>              :   }) async {</span>
<span id="L192"><span class="lineNum">     192</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; data = &lt;String, dynamic&gt;{</span></span>
<span id="L193"><span class="lineNum">     193</span>              :       'payment_method_id': paymentMethodId,</span>
<span id="L194"><span class="lineNum">     194</span>              :       'voucher_ids': voucherIds,</span>
<span id="L195"><span class="lineNum">     195</span>              :     };</span>
<span id="L196"><span class="lineNum">     196</span>              : </span>
<span id="L197"><span class="lineNum">     197</span>              :     if (flowType != null) {</span>
<span id="L198"><span class="lineNum">     198</span> <span class="tlaUNC">           0 :       data['flow_type'] = flowType.value;</span></span>
<span id="L199"><span class="lineNum">     199</span>              :     }</span>
<span id="L200"><span class="lineNum">     200</span>              : </span>
<span id="L201"><span class="lineNum">     201</span>              :     if (emiOfferId != null) {</span>
<span id="L202"><span class="lineNum">     202</span> <span class="tlaUNC">           0 :       data['emi_offer_id'] = emiOfferId;</span></span>
<span id="L203"><span class="lineNum">     203</span>              :     }</span>
<span id="L204"><span class="lineNum">     204</span>              : </span>
<span id="L205"><span class="lineNum">     205</span>              :     final BaseResponse baseResponse =</span>
<span id="L206"><span class="lineNum">     206</span> <span class="tlaUNC">           0 :         await client.patch('$checkOutUrl/$sessionId', data: data, mockConfig: mockConfig);</span></span>
<span id="L207"><span class="lineNum">     207</span> <span class="tlaUNC">           0 :     final UpdateOrderEntity updateOrderEntity = commonUtilFunction.serialize(</span></span>
<span id="L208"><span class="lineNum">     208</span> <span class="tlaUNC">           0 :             () =&gt; UpdateOrderEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L209"><span class="lineNum">     209</span>              :             originalData: baseResponse) ??</span>
<span id="L210"><span class="lineNum">     210</span> <span class="tlaUNC">           0 :         UpdateOrderEntity.unserializable();</span></span>
<span id="L211"><span class="lineNum">     211</span>              :     return updateOrderEntity;</span>
<span id="L212"><span class="lineNum">     212</span>              :   }</span>
<span id="L213"><span class="lineNum">     213</span>              : </span>
<span id="L214"><span class="lineNum">     214</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L215"><span class="lineNum">     215</span>              :   Future&lt;TransactionHistoryEntity&gt; getTransactionsHistory({</span>
<span id="L216"><span class="lineNum">     216</span>              :     required TransactionHistoryRequest request,</span>
<span id="L217"><span class="lineNum">     217</span>              :     MockConfig? mockConfig,</span>
<span id="L218"><span class="lineNum">     218</span>              :   }) async {</span>
<span id="L219"><span class="lineNum">     219</span>              :     final BaseResponse baseResponse =</span>
<span id="L220"><span class="lineNum">     220</span> <span class="tlaUNC">           0 :         await client.get(transactionUrl, params: request.toJson(), mockConfig: mockConfig);</span></span>
<span id="L221"><span class="lineNum">     221</span> <span class="tlaUNC">           0 :     final TransactionHistoryEntity transactionHistoryEntity = commonUtilFunction.serialize(</span></span>
<span id="L222"><span class="lineNum">     222</span> <span class="tlaUNC">           0 :             () =&gt; TransactionHistoryEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L223"><span class="lineNum">     223</span>              :             originalData: baseResponse) ??</span>
<span id="L224"><span class="lineNum">     224</span> <span class="tlaUNC">           0 :         TransactionHistoryEntity.unserializable();</span></span>
<span id="L225"><span class="lineNum">     225</span>              :     return transactionHistoryEntity;</span>
<span id="L226"><span class="lineNum">     226</span>              :   }</span>
<span id="L227"><span class="lineNum">     227</span>              : </span>
<span id="L228"><span class="lineNum">     228</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L229"><span class="lineNum">     229</span>              :   Future&lt;TransactionHistoryEntity&gt; getTransactionsHistoryV2({</span>
<span id="L230"><span class="lineNum">     230</span>              :     required TransactionHistoryRequestV2 request,</span>
<span id="L231"><span class="lineNum">     231</span>              :     MockConfig? mockConfig,</span>
<span id="L232"><span class="lineNum">     232</span>              :   }) async {</span>
<span id="L233"><span class="lineNum">     233</span>              :     final BaseResponse baseResponse =</span>
<span id="L234"><span class="lineNum">     234</span> <span class="tlaUNC">           0 :         await client.get(transactionV2Url, params: request.toJson(), mockConfig: mockConfig);</span></span>
<span id="L235"><span class="lineNum">     235</span> <span class="tlaUNC">           0 :     final TransactionHistoryEntity transactionHistoryEntity = commonUtilFunction.serialize(</span></span>
<span id="L236"><span class="lineNum">     236</span> <span class="tlaUNC">           0 :             () =&gt; TransactionHistoryEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L237"><span class="lineNum">     237</span>              :             originalData: baseResponse) ??</span>
<span id="L238"><span class="lineNum">     238</span> <span class="tlaUNC">           0 :         TransactionHistoryEntity.unserializable();</span></span>
<span id="L239"><span class="lineNum">     239</span>              :     return transactionHistoryEntity;</span>
<span id="L240"><span class="lineNum">     240</span>              :   }</span>
<span id="L241"><span class="lineNum">     241</span>              : </span>
<span id="L242"><span class="lineNum">     242</span> <span class="tlaUNC">           0 :   @override</span></span>
<span id="L243"><span class="lineNum">     243</span>              :   Future&lt;CloneOrderEntity&gt; cloneOrder({</span>
<span id="L244"><span class="lineNum">     244</span>              :     required String? sessionId,</span>
<span id="L245"><span class="lineNum">     245</span>              :     MockConfig? mockConfig,</span>
<span id="L246"><span class="lineNum">     246</span>              :   }) async {</span>
<span id="L247"><span class="lineNum">     247</span>              :     final BaseResponse baseResponse =</span>
<span id="L248"><span class="lineNum">     248</span> <span class="tlaUNC">           0 :         await client.post(cloneOrderUrl(sessionId ?? ''), mockConfig: mockConfig);</span></span>
<span id="L249"><span class="lineNum">     249</span> <span class="tlaUNC">           0 :     final CloneOrderEntity cloneOrderEntity = commonUtilFunction.serialize(</span></span>
<span id="L250"><span class="lineNum">     250</span> <span class="tlaUNC">           0 :           () =&gt; CloneOrderEntity.fromBaseResponse(baseResponse),</span></span>
<span id="L251"><span class="lineNum">     251</span>              :           originalData: baseResponse,</span>
<span id="L252"><span class="lineNum">     252</span>              :         ) ??</span>
<span id="L253"><span class="lineNum">     253</span> <span class="tlaUNC">           0 :         CloneOrderEntity.unserializable();</span></span>
<span id="L254"><span class="lineNum">     254</span>              :     return cloneOrderEntity;</span>
<span id="L255"><span class="lineNum">     255</span>              :   }</span>
<span id="L256"><span class="lineNum">     256</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
