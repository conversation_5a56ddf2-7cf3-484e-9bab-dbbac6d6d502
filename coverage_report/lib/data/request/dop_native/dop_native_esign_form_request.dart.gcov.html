<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/request/dop_native/dop_native_esign_form_request.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/request/dop_native">lib/data/request/dop_native</a> - dop_native_esign_form_request.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">26</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : class DOPNativeESignFormRequest {</span>
<span id="L2"><span class="lineNum">       2</span>              :   final bool? changeState;</span>
<span id="L3"><span class="lineNum">       3</span>              :   final String? formStep;</span>
<span id="L4"><span class="lineNum">       4</span>              :   final DOPNativeESignFormData? formData;</span>
<span id="L5"><span class="lineNum">       5</span>              : </span>
<span id="L6"><span class="lineNum">       6</span> <span class="tlaUNC">           0 :   const DOPNativeESignFormRequest({</span></span>
<span id="L7"><span class="lineNum">       7</span>              :     this.changeState,</span>
<span id="L8"><span class="lineNum">       8</span>              :     this.formStep,</span>
<span id="L9"><span class="lineNum">       9</span>              :     this.formData,</span>
<span id="L10"><span class="lineNum">      10</span>              :   });</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt;? toJson() {</span></span>
<span id="L13"><span class="lineNum">      13</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; data = &lt;String, dynamic&gt;{</span></span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaUNC">           0 :       'change_state': changeState,</span></span>
<span id="L15"><span class="lineNum">      15</span> <span class="tlaUNC">           0 :       'form_step': formStep,</span></span>
<span id="L16"><span class="lineNum">      16</span> <span class="tlaUNC">           0 :       'form_data': formData?.toJson(),</span></span>
<span id="L17"><span class="lineNum">      17</span>              :     };</span>
<span id="L18"><span class="lineNum">      18</span>              : </span>
<span id="L19"><span class="lineNum">      19</span> <span class="tlaUNC">           0 :     data.removeWhere((_, dynamic value) {</span></span>
<span id="L20"><span class="lineNum">      20</span>              :       return value == null;</span>
<span id="L21"><span class="lineNum">      21</span>              :     });</span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaUNC">           0 :     if (data.isEmpty) {</span></span>
<span id="L23"><span class="lineNum">      23</span>              :       return null;</span>
<span id="L24"><span class="lineNum">      24</span>              :     }</span>
<span id="L25"><span class="lineNum">      25</span>              :     return data;</span>
<span id="L26"><span class="lineNum">      26</span>              :   }</span>
<span id="L27"><span class="lineNum">      27</span>              : }</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              : class DOPNativeESignFormData {</span>
<span id="L30"><span class="lineNum">      30</span>              :   final bool? isUSNationality;</span>
<span id="L31"><span class="lineNum">      31</span>              :   final bool? isBornInUS;</span>
<span id="L32"><span class="lineNum">      32</span>              :   final bool? isInstructDepositToOrWithdrawFromUSAddress;</span>
<span id="L33"><span class="lineNum">      33</span>              :   final bool? isHaveUSAddressOrUSNumber;</span>
<span id="L34"><span class="lineNum">      34</span>              :   final bool? isDelegateToUSAddress;</span>
<span id="L35"><span class="lineNum">      35</span>              :   final bool? isHaveUniqueUSMailAddress;</span>
<span id="L36"><span class="lineNum">      36</span>              :   final LinkCardToEvo? payment;</span>
<span id="L37"><span class="lineNum">      37</span>              : </span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :   const DOPNativeESignFormData({</span></span>
<span id="L39"><span class="lineNum">      39</span>              :     this.isUSNationality,</span>
<span id="L40"><span class="lineNum">      40</span>              :     this.isBornInUS,</span>
<span id="L41"><span class="lineNum">      41</span>              :     this.isInstructDepositToOrWithdrawFromUSAddress,</span>
<span id="L42"><span class="lineNum">      42</span>              :     this.isHaveUSAddressOrUSNumber,</span>
<span id="L43"><span class="lineNum">      43</span>              :     this.isDelegateToUSAddress,</span>
<span id="L44"><span class="lineNum">      44</span>              :     this.isHaveUniqueUSMailAddress,</span>
<span id="L45"><span class="lineNum">      45</span>              :     this.payment,</span>
<span id="L46"><span class="lineNum">      46</span>              :   });</span>
<span id="L47"><span class="lineNum">      47</span>              : </span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt;? toJson() {</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = &lt;String, dynamic&gt;{</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :       'isUSNationality': isUSNationality,</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :       'isBornInUS': isBornInUS,</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :       'isInstructDepositToOrWithdrawFromUSAddress': isInstructDepositToOrWithdrawFromUSAddress,</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :       'isHaveUSAddressOrUSNumber': isHaveUSAddressOrUSNumber,</span></span>
<span id="L54"><span class="lineNum">      54</span> <span class="tlaUNC">           0 :       'isDelegateToUSAddress': isDelegateToUSAddress,</span></span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :       'isHaveUniqueUSMailAddress': isHaveUniqueUSMailAddress,</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :       'payment': payment?.toJson(),</span></span>
<span id="L57"><span class="lineNum">      57</span>              :     };</span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :     json.removeWhere((_, dynamic value) {</span></span>
<span id="L59"><span class="lineNum">      59</span>              :       return value == null;</span>
<span id="L60"><span class="lineNum">      60</span>              :     });</span>
<span id="L61"><span class="lineNum">      61</span> <span class="tlaUNC">           0 :     if (json.isEmpty) {</span></span>
<span id="L62"><span class="lineNum">      62</span>              :       return null;</span>
<span id="L63"><span class="lineNum">      63</span>              :     }</span>
<span id="L64"><span class="lineNum">      64</span>              :     return json;</span>
<span id="L65"><span class="lineNum">      65</span>              :   }</span>
<span id="L66"><span class="lineNum">      66</span>              : }</span>
<span id="L67"><span class="lineNum">      67</span>              : </span>
<span id="L68"><span class="lineNum">      68</span>              : class LinkCardToEvo {</span>
<span id="L69"><span class="lineNum">      69</span>              :   final bool? linkToken;</span>
<span id="L70"><span class="lineNum">      70</span>              : </span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :   const LinkCardToEvo({this.linkToken});</span></span>
<span id="L72"><span class="lineNum">      72</span>              : </span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt;? toJson() {</span></span>
<span id="L74"><span class="lineNum">      74</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; data = &lt;String, dynamic&gt;{</span></span>
<span id="L75"><span class="lineNum">      75</span> <span class="tlaUNC">           0 :       'link_token': linkToken,</span></span>
<span id="L76"><span class="lineNum">      76</span>              :     };</span>
<span id="L77"><span class="lineNum">      77</span> <span class="tlaUNC">           0 :     data.removeWhere((_, dynamic value) {</span></span>
<span id="L78"><span class="lineNum">      78</span>              :       return value == null;</span>
<span id="L79"><span class="lineNum">      79</span>              :     });</span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :     if (data.isEmpty) {</span></span>
<span id="L81"><span class="lineNum">      81</span>              :       return null;</span>
<span id="L82"><span class="lineNum">      82</span>              :     }</span>
<span id="L83"><span class="lineNum">      83</span>              :     return data;</span>
<span id="L84"><span class="lineNum">      84</span>              :   }</span>
<span id="L85"><span class="lineNum">      85</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
