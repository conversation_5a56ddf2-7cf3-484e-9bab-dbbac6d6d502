<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/request/dop_native/dop_native_ekyc_confirm_request.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/request/dop_native">lib/data/request/dop_native</a> - dop_native_ekyc_confirm_request.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">43</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import '../../../feature/dop_native/features/ekyc_ui_only/id_verification_confirm/models/dop_native_ocr_data_model.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../../../feature/dop_native/util/dop_functions.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../response/dop_native/dop_native_contact_info_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../response/dop_native/dop_native_personal_info_entity.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../response/dop_native/dop_native_working_info_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : </span>
<span id="L7"><span class="lineNum">       7</span>              : class DOPNativeEkycConfirmRequest {</span>
<span id="L8"><span class="lineNum">       8</span>              :   final DOPNativeContactInfoEntity? contactInfo;</span>
<span id="L9"><span class="lineNum">       9</span>              :   final DOPNativePersonalInfoEntity? personalInfo;</span>
<span id="L10"><span class="lineNum">      10</span>              :   final DOPNativeWorkingInfoEntity? workingInfo;</span>
<span id="L11"><span class="lineNum">      11</span>              : </span>
<span id="L12"><span class="lineNum">      12</span> <span class="tlaUNC">           0 :   DOPNativeEkycConfirmRequest({</span></span>
<span id="L13"><span class="lineNum">      13</span>              :     this.contactInfo,</span>
<span id="L14"><span class="lineNum">      14</span>              :     this.personalInfo,</span>
<span id="L15"><span class="lineNum">      15</span>              :     this.workingInfo,</span>
<span id="L16"><span class="lineNum">      16</span>              :   });</span>
<span id="L17"><span class="lineNum">      17</span>              : </span>
<span id="L18"><span class="lineNum">      18</span>              :   /// Returned json should exclude null value field</span>
<span id="L19"><span class="lineNum">      19</span>              :   /// Refer: https://trustingsocial.slack.com/archives/C06BBSRR99P/p1714891289576959</span>
<span id="L20"><span class="lineNum">      20</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() {</span></span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; updatedFields = &lt;String, dynamic&gt;{</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaUNC">           0 :       'contactInfo': contactInfo?.toJson(),</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :       'personalInfo': personalInfo?.toJson(),</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :       'workingInfo': workingInfo?.toJson(),</span></span>
<span id="L25"><span class="lineNum">      25</span>              :     };</span>
<span id="L26"><span class="lineNum">      26</span> <span class="tlaUNC">           0 :     updatedFields.removeWhere((_, dynamic value) =&gt; value == null);</span></span>
<span id="L27"><span class="lineNum">      27</span> <span class="tlaUNC">           0 :     return &lt;String, dynamic&gt;{</span></span>
<span id="L28"><span class="lineNum">      28</span>              :       'updated_fields': updatedFields,</span>
<span id="L29"><span class="lineNum">      29</span>              :     };</span>
<span id="L30"><span class="lineNum">      30</span>              :   }</span>
<span id="L31"><span class="lineNum">      31</span>              : </span>
<span id="L32"><span class="lineNum">      32</span> <span class="tlaUNC">           0 :   factory DOPNativeEkycConfirmRequest.fromOcrDataModel(DOPNativeOCRDataModel ocrData) {</span></span>
<span id="L33"><span class="lineNum">      33</span> <span class="tlaUNC">           0 :     final String? genderValue = DOPNativeGender.toValue(ocrData.gender);</span></span>
<span id="L34"><span class="lineNum">      34</span>              : </span>
<span id="L35"><span class="lineNum">      35</span> <span class="tlaUNC">           0 :     return DOPNativeEkycConfirmRequest(</span></span>
<span id="L36"><span class="lineNum">      36</span> <span class="tlaUNC">           0 :       contactInfo: DOPNativeContactInfoEntity(</span></span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :         familyAddress: ocrData.familyAddress,</span></span>
<span id="L38"><span class="lineNum">      38</span> <span class="tlaUNC">           0 :         familyBookAddressDistId: ocrData.familyBookAddressDistId,</span></span>
<span id="L39"><span class="lineNum">      39</span> <span class="tlaUNC">           0 :         familyBookAddressProvinceId: ocrData.familyBookAddressProvinceId,</span></span>
<span id="L40"><span class="lineNum">      40</span> <span class="tlaUNC">           0 :         familyBookAddressWardId: ocrData.familyBookAddressWardId,</span></span>
<span id="L41"><span class="lineNum">      41</span> <span class="tlaUNC">           0 :         curAddressDistId: ocrData.curAddressDistId,</span></span>
<span id="L42"><span class="lineNum">      42</span> <span class="tlaUNC">           0 :         curAddressProvinceId: ocrData.curAddressProvinceId,</span></span>
<span id="L43"><span class="lineNum">      43</span> <span class="tlaUNC">           0 :         curAddressWardId: ocrData.curAddressWardId,</span></span>
<span id="L44"><span class="lineNum">      44</span>              :       ),</span>
<span id="L45"><span class="lineNum">      45</span> <span class="tlaUNC">           0 :       personalInfo: DOPNativePersonalInfoEntity(</span></span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :         birthday: ocrData.birthday,</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :         fullName: ocrData.fullName,</span></span>
<span id="L48"><span class="lineNum">      48</span>              :         gender: genderValue,</span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :         idCard: ocrData.idCard,</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :         idIssueDate: ocrData.idIssueDate,</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :         idIssuePlaceId: ocrData.idIssuePlaceId,</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :         oldIDCard: getOldIDCardToSave(ocrData.oldIDCard),</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :         email: ocrData.email,</span></span>
<span id="L54"><span class="lineNum">      54</span>              :       ),</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :       workingInfo: DOPNativeWorkingInfoEntity(</span></span>
<span id="L56"><span class="lineNum">      56</span> <span class="tlaUNC">           0 :         employmentId: ocrData.employmentId,</span></span>
<span id="L57"><span class="lineNum">      57</span> <span class="tlaUNC">           0 :         employmentStatusId: ocrData.employmentStatusId,</span></span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :         income: dopUtilFunction.convertIncome(ocrData.income),</span></span>
<span id="L59"><span class="lineNum">      59</span> <span class="tlaUNC">           0 :         salaryPaymentMethodId: ocrData.salaryPaymentMethodId,</span></span>
<span id="L60"><span class="lineNum">      60</span>              :       ),</span>
<span id="L61"><span class="lineNum">      61</span>              :     );</span>
<span id="L62"><span class="lineNum">      62</span>              :   }</span>
<span id="L63"><span class="lineNum">      63</span>              : </span>
<span id="L64"><span class="lineNum">      64</span> <span class="tlaUNC">           0 :   factory DOPNativeEkycConfirmRequest.fromOcrDataModelForSingleOCR(DOPNativeOCRDataModel ocrData) {</span></span>
<span id="L65"><span class="lineNum">      65</span> <span class="tlaUNC">           0 :     return DOPNativeEkycConfirmRequest(</span></span>
<span id="L66"><span class="lineNum">      66</span> <span class="tlaUNC">           0 :       personalInfo: DOPNativePersonalInfoEntity(</span></span>
<span id="L67"><span class="lineNum">      67</span> <span class="tlaUNC">           0 :         oldIDCard: getOldIDCardToSave(ocrData.oldIDCard),</span></span>
<span id="L68"><span class="lineNum">      68</span>              :       ),</span>
<span id="L69"><span class="lineNum">      69</span> <span class="tlaUNC">           0 :       contactInfo: DOPNativeContactInfoEntity(</span></span>
<span id="L70"><span class="lineNum">      70</span> <span class="tlaUNC">           0 :         familyAddress: ocrData.familyAddress,</span></span>
<span id="L71"><span class="lineNum">      71</span> <span class="tlaUNC">           0 :         familyBookAddressDistId: ocrData.familyBookAddressDistId,</span></span>
<span id="L72"><span class="lineNum">      72</span> <span class="tlaUNC">           0 :         familyBookAddressProvinceId: ocrData.familyBookAddressProvinceId,</span></span>
<span id="L73"><span class="lineNum">      73</span> <span class="tlaUNC">           0 :         familyBookAddressWardId: ocrData.familyBookAddressWardId,</span></span>
<span id="L74"><span class="lineNum">      74</span>              :       ),</span>
<span id="L75"><span class="lineNum">      75</span>              :     );</span>
<span id="L76"><span class="lineNum">      76</span>              :   }</span>
<span id="L77"><span class="lineNum">      77</span>              : </span>
<span id="L78"><span class="lineNum">      78</span>              :   /// Since [oldIDCard] is optional field,</span>
<span id="L79"><span class="lineNum">      79</span>              :   /// if the user inputs the wrong value, we will not save it.</span>
<span id="L80"><span class="lineNum">      80</span> <span class="tlaUNC">           0 :   static String? getOldIDCardToSave(String? oldId) {</span></span>
<span id="L81"><span class="lineNum">      81</span> <span class="tlaUNC">           0 :     final bool isValidOldId = dopUtilFunction.isVietnameseCitizenIdCardBefore2016(oldId);</span></span>
<span id="L82"><span class="lineNum">      82</span>              : </span>
<span id="L83"><span class="lineNum">      83</span>              :     return isValidOldId ? oldId : null;</span>
<span id="L84"><span class="lineNum">      84</span>              :   }</span>
<span id="L85"><span class="lineNum">      85</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
