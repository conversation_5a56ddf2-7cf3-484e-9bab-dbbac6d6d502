<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html lang="en">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>LCOV - lcov.info - lib/data/request/dop_native/dop_native_application_submit_form_request.dart</title>
  <link rel="stylesheet" type="text/css" href="../../../../gcov.css">
</head>

<body>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="title">LCOV - code coverage report</td></tr>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>

            <tr>
              <td width="100%">
                <table cellpadding=1 border=0 width="100%">
          <tr>
            <td width="10%" class="headerItem">Current view:</td>
            <td width="10%" class="headerValue"><a href="../../../../index.html" title="Click to go to top-level">top level</a> - <a href="index.html" title="Click to go to directory lib/data/request/dop_native">lib/data/request/dop_native</a> - dop_native_application_submit_form_request.dart</td>
            <td width="5%"></td>
            <td width="5%"></td>
            <td width="5%" class="headerCovTableHead">Coverage</td>
            <td width="5%" class="headerCovTableHead" title="Covered + Uncovered code">Total</td>
            <td width="5%" class="headerCovTableHead" title="Exercised code only">Hit</td>
          </tr>
          <tr>
            <td class="headerItem">Test:</td>
            <td class="headerValue">lcov.info</td>
            <td></td>
            <td class="headerItem">Lines:</td>
            <td class="headerCovTableEntryLo">0.0&nbsp;%</td>
            <td class="headerCovTableEntry">17</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
          <tr>
            <td class="headerItem">Test Date:</td>
            <td class="headerValue">2025-06-04 09:31:10</td>
            <td></td>
            <td class="headerItem">Functions:</td>
            <td class="headerCovTableEntryHi">-</td>
            <td class="headerCovTableEntry">0</td>
            <td class="headerCovTableEntry">0</td>
          </tr>
                  <tr><td><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
                </table>
              </td>
            </tr>

            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
          </table>

          <table cellpadding=0 cellspacing=0 border=0>
            <tr>
              <td><br></td>
            </tr>
            <tr>
              <td>
<pre class="sourceHeading">            Line data    Source code</pre>
<pre class="source">
<span id="L1"><span class="lineNum">       1</span>              : import '../../response/dop_native/dop_native_contact_info_entity.dart';</span>
<span id="L2"><span class="lineNum">       2</span>              : import '../../response/dop_native/dop_native_personal_info_entity.dart';</span>
<span id="L3"><span class="lineNum">       3</span>              : import '../../response/dop_native/dop_native_reference_info_entity.dart';</span>
<span id="L4"><span class="lineNum">       4</span>              : import '../../response/dop_native/dop_native_reward_info_entity.dart';</span>
<span id="L5"><span class="lineNum">       5</span>              : import '../../response/dop_native/dop_native_salesman_info_entity.dart';</span>
<span id="L6"><span class="lineNum">       6</span>              : import '../../response/dop_native/dop_native_working_info_entity.dart';</span>
<span id="L7"><span class="lineNum">       7</span>              : </span>
<span id="L8"><span class="lineNum">       8</span>              : class DOPNativeApplicationSubmitFormRequest {</span>
<span id="L9"><span class="lineNum">       9</span>              :   final String? formAppState;</span>
<span id="L10"><span class="lineNum">      10</span>              :   final bool? changeState;</span>
<span id="L11"><span class="lineNum">      11</span>              :   final String? formStep;</span>
<span id="L12"><span class="lineNum">      12</span>              :   final ApplicationFormData? formData;</span>
<span id="L13"><span class="lineNum">      13</span>              : </span>
<span id="L14"><span class="lineNum">      14</span> <span class="tlaUNC">           0 :   DOPNativeApplicationSubmitFormRequest({</span></span>
<span id="L15"><span class="lineNum">      15</span>              :     this.formAppState,</span>
<span id="L16"><span class="lineNum">      16</span>              :     this.changeState,</span>
<span id="L17"><span class="lineNum">      17</span>              :     this.formStep,</span>
<span id="L18"><span class="lineNum">      18</span>              :     this.formData,</span>
<span id="L19"><span class="lineNum">      19</span>              :   });</span>
<span id="L20"><span class="lineNum">      20</span>              : </span>
<span id="L21"><span class="lineNum">      21</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt; toJson() =&gt; &lt;String, dynamic&gt;{</span></span>
<span id="L22"><span class="lineNum">      22</span> <span class="tlaUNC">           0 :         'form_app_state': formAppState,</span></span>
<span id="L23"><span class="lineNum">      23</span> <span class="tlaUNC">           0 :         'change_state': changeState,</span></span>
<span id="L24"><span class="lineNum">      24</span> <span class="tlaUNC">           0 :         'form_step': formStep,</span></span>
<span id="L25"><span class="lineNum">      25</span> <span class="tlaUNC">           0 :         'form_data': formData?.toJson(),</span></span>
<span id="L26"><span class="lineNum">      26</span>              :       };</span>
<span id="L27"><span class="lineNum">      27</span>              : }</span>
<span id="L28"><span class="lineNum">      28</span>              : </span>
<span id="L29"><span class="lineNum">      29</span>              : class ApplicationFormData {</span>
<span id="L30"><span class="lineNum">      30</span>              :   final DOPNativeContactInfoEntity? contactInfo;</span>
<span id="L31"><span class="lineNum">      31</span>              :   final DOPNativePersonalInfoEntity? personalInfo;</span>
<span id="L32"><span class="lineNum">      32</span>              :   final DOPNativeWorkingInfoEntity? workingInfo;</span>
<span id="L33"><span class="lineNum">      33</span>              :   final DOPNativeReferenceInfoEntity? referenceInfo;</span>
<span id="L34"><span class="lineNum">      34</span>              :   final DOPNativeRewardInfoEntity? rewardInfo;</span>
<span id="L35"><span class="lineNum">      35</span>              :   final DOPNativeSalesmanInfoEntity? salesmanInfo;</span>
<span id="L36"><span class="lineNum">      36</span>              : </span>
<span id="L37"><span class="lineNum">      37</span> <span class="tlaUNC">           0 :   ApplicationFormData({</span></span>
<span id="L38"><span class="lineNum">      38</span>              :     this.contactInfo,</span>
<span id="L39"><span class="lineNum">      39</span>              :     this.personalInfo,</span>
<span id="L40"><span class="lineNum">      40</span>              :     this.workingInfo,</span>
<span id="L41"><span class="lineNum">      41</span>              :     this.referenceInfo,</span>
<span id="L42"><span class="lineNum">      42</span>              :     this.rewardInfo,</span>
<span id="L43"><span class="lineNum">      43</span>              :     this.salesmanInfo,</span>
<span id="L44"><span class="lineNum">      44</span>              :   });</span>
<span id="L45"><span class="lineNum">      45</span>              : </span>
<span id="L46"><span class="lineNum">      46</span> <span class="tlaUNC">           0 :   Map&lt;String, dynamic&gt;? toJson() {</span></span>
<span id="L47"><span class="lineNum">      47</span> <span class="tlaUNC">           0 :     final Map&lt;String, dynamic&gt; json = &lt;String, dynamic&gt;{</span></span>
<span id="L48"><span class="lineNum">      48</span> <span class="tlaUNC">           0 :       'contactInfo': contactInfo?.toJson(),</span></span>
<span id="L49"><span class="lineNum">      49</span> <span class="tlaUNC">           0 :       'personalInfo': personalInfo?.toJson(),</span></span>
<span id="L50"><span class="lineNum">      50</span> <span class="tlaUNC">           0 :       'workingInfo': workingInfo?.toJson(),</span></span>
<span id="L51"><span class="lineNum">      51</span> <span class="tlaUNC">           0 :       'referenceInfo': referenceInfo?.toJson(),</span></span>
<span id="L52"><span class="lineNum">      52</span> <span class="tlaUNC">           0 :       'rewardInfo': rewardInfo?.toJson(),</span></span>
<span id="L53"><span class="lineNum">      53</span> <span class="tlaUNC">           0 :       'salemanInfo': salesmanInfo?.toJson(),</span></span>
<span id="L54"><span class="lineNum">      54</span>              :     };</span>
<span id="L55"><span class="lineNum">      55</span> <span class="tlaUNC">           0 :     json.removeWhere((_, dynamic value) {</span></span>
<span id="L56"><span class="lineNum">      56</span>              :       return value == null;</span>
<span id="L57"><span class="lineNum">      57</span>              :     });</span>
<span id="L58"><span class="lineNum">      58</span> <span class="tlaUNC">           0 :     if (json.isEmpty) {</span></span>
<span id="L59"><span class="lineNum">      59</span>              :       return null;</span>
<span id="L60"><span class="lineNum">      60</span>              :     }</span>
<span id="L61"><span class="lineNum">      61</span>              :     return json;</span>
<span id="L62"><span class="lineNum">      62</span>              :   }</span>
<span id="L63"><span class="lineNum">      63</span>              : }</span>
        </pre>
              </td>
            </tr>
          </table>
          <br>

          <table width="100%" border=0 cellspacing=0 cellpadding=0>
            <tr><td class="ruler"><img src="../../../../glass.png" width=3 height=3 alt=""></td></tr>
            <tr><td class="versionInfo">Generated by: <a href="https://github.com//linux-test-project/lcov" target="_parent">LCOV version 2.3-1</a></td></tr>
          </table>
          <br>

</body>
</html>
